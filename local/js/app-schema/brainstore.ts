import { z } from "zod";

export const backfillableObjectTypeSchema = z.enum([
  "experiment",
  "dataset",
  "project_logs",
  "playground_logs",
]);
export type BackfillableObjectType = z.infer<
  typeof backfillableObjectTypeSchema
>;

export const trackedObjectSchema = z.object({
  project_id: z.string(),
  object_type: backfillableObjectTypeSchema,
});
export type TrackedObject = z.infer<typeof trackedObjectSchema>;

export const fullTrackedObjectSchema = trackedObjectSchema.extend({
  org_id: z.string().nullable(),
});
export type FullTrackedObject = z.infer<typeof fullTrackedObjectSchema>;

const brainstoreBackfillStatusSchema = z.object({
  // The "backfill frontier" for this group of objects. Everything before this
  // sequence id has been backfilled for this group of objects.
  //
  // For the logs table, these are 32-bit so will fit. For the logs2 table, the
  // sequence IDs are 64-bit so they may eventually overflow and incur rounding
  // error, but for now we just use numbers.
  backfill_frontier_sequence_id: z.number().nullable(),

  // The "backfill target" for this group of objects. This is the sequence ID we
  // must reach to consider the backfill complete.
  backfill_target_sequence_id: z.number().nullable(),

  // The last time that we backfilled anything in this group of objects.
  last_backfilled_ts: z.string().datetime().nullable(),

  // The timestamp at which we completed the initial backfill.
  completed_initial_backfill_ts: z.string().datetime().nullable(),

  // Progress estimate for completing the backfill, as a number between 0-1.
  estimated_progress: z.number().nullable(),

  // Whether or not the group of objects is currently being actively backfilled.
  enabled: z.boolean(),
});

export const objectBackfillStatusSchema =
  brainstoreBackfillStatusSchema.and(trackedObjectSchema);
export type ObjectBackfillStatus = z.infer<typeof objectBackfillStatusSchema>;

export const globalBackfillStatusSchema = z.object({
  // Whether or not all actively-backfilled objects have been fully backfilled.
  // This will be vacuously true if there are no actively-backfilled objects.
  all_backfilled: z.boolean().nullish(),

  // The maximum sequence ID we have seen in the logs at the time of invocation.
  // We can consider the DB fully-backfilled up to this sequence ID if the
  // backfill_frontier_sequence_id with respect to the full set of tracked
  // objects is >= this value.
  logs_max_sequence_id: z.number().nullish(),
  logs2_max_sequence_id: z.number().nullish(),

  // The the maximum sequence id the backfill process has considered.
  backfill_frontier_sequence_id: z.number().nullish(),
  backfill_frontier_sequence_id_2: z.number().nullish(),

  // The historical backfill progress estimate, as a number between 0-1.
  historical_backfill_progress: z.number().nullish(),

  // The comments backfill progress estimate, as a number between 0-1.
  comments_backfill_progress: z.number().nullish(),

  // The timestamp at which we completed the initial comments backfill.
  completed_initial_comments_backfill_ts: z.string().datetime().nullish(),

  // The mode in which the backfill is running.
  backfill_mode: z
    .enum(["per_project", "historical_full"])
    .default("per_project"),
});

export type GlobalBackfillStatus = z.infer<typeof globalBackfillStatusSchema>;

export const projectBackfillStatusSchema = z.object({
  global_status: globalBackfillStatusSchema,
  object_statuses: z.array(objectBackfillStatusSchema),
});
export type ProjectBackfillStatus = z.infer<typeof projectBackfillStatusSchema>;

export const storageMetaSchema = z.object({
  segments: z.array(z.object({ segment_id: z.string() })),
});

export const lastCompactedIndexMetaSchema = z.object({
  xact_id: z.string(),
  storage_meta: storageMetaSchema,
});

export const lastIndexOperationSchema = z.object({
  start: z.string().datetime().nullish(),
  last_updated: z.string().datetime().nullish(),
  finished: z.boolean().nullish(),
  estimated_progress: z.number().nullish(),
  stage: z.string().nullish(),
  error: z.string().nullish(),
  details: z
    .union([
      z.object({
        compact: z.object({
          num_wal_entries: z.number(),
        }),
      }),
      z.object({
        merge: z.object({
          merges: z.array(z.array(z.string())),
        }),
      }),
    ])
    .nullish(),
});
export type LastIndexOperation = z.infer<typeof lastIndexOperationSchema>;

export const segmentMetaSchema = z.object({
  minimum_pagination_key: z.string(),
  stats: z
    .record(
      z
        .object({
          min: z.string(),
          max: z.string(),
        })
        .nullish(),
    )
    .nullish(),
  num_rows: z.number(),
  last_compacted_index_meta: lastCompactedIndexMetaSchema.nullable(),
  last_processed_xact_id: z.string().nullable(),
  last_index_operation: lastIndexOperationSchema.nullable(),
});

export const objectInfoSchema = z.object({
  object_id: z.string(),
  last_processed_xact_id: z.string().nullable(),
  segments: z.record(z.string(), segmentMetaSchema),
});
export type ObjectInfo = z.infer<typeof objectInfoSchema>;

export const objectInfoWithBackfillStatusSchema = objectInfoSchema.merge(
  brainstoreBackfillStatusSchema,
);
export type ObjectInfoWithBackfillStatus = z.infer<
  typeof objectInfoWithBackfillStatusSchema
>;

export const lastIndexOperationWithObjectId = lastIndexOperationSchema.extend({
  segment_id: z.string(),
  object_id: z.string(),
});

export type LastIndexOperationWithObjectId = z.infer<
  typeof lastIndexOperationWithObjectId
>;

export const brainstoreSystemStatus = z.object({
  status: z.enum(["ok", "error"]),
  uptime: z.number(),
  services: z.record(
    z.string(),
    z.object({
      success: z.boolean(),
      message: z.string().nullish(),
      error: z.string().nullish(),
    }),
  ),
  system: z
    .object({
      memory: z
        .object({
          total: z.number(),
          used: z.number(),
          proc: z.number().nullish(),
        })
        .nullish(),
      process: z
        .object({
          threads: z.number().nullish(),
        })
        .nullish(),
      disk: z
        .object({
          total: z.number().nullish(),
          free: z.number().nullish(),
        })
        .nullish(),
    })
    .nullish(),
});
export type BrainstoreSystemStatus = z.infer<typeof brainstoreSystemStatus>;

export const brainstoreOptimizationStepSchema = z.object({
  segment_id: z.string(),
  compact: z.boolean(),
  merge: z.boolean(),
});
export type BrainstoreOptimizationStep = z.infer<
  typeof brainstoreOptimizationStepSchema
>;

export const brainstoreOptimizationResultSchema = z.object({
  performed: z.array(brainstoreOptimizationStepSchema),
  planned: z.array(brainstoreOptimizationStepSchema),
});
export type BrainstoreOptimizationResult = z.infer<
  typeof brainstoreOptimizationResultSchema
>;

export const globalVacuumStatusSchema = z.object({
  num_live_segments: z.number(),
  all_vacuumed_once: z.boolean().nullish(),
  all_vacuumed_up_to_date: z.boolean().nullish(),
  did_delete_files: z.boolean().nullish(),
});

export type GlobalVacuumStatus = z.infer<typeof globalVacuumStatusSchema>;

// Keep this in sync with IndexWalReaderRealtimeState in brainstore/storage/src/index_wal_reader.rs
export const realtimeStateSchema = z.discriminatedUnion("type", [
  z.object({
    type: z.literal("disabled"),
  }),
  z.object({
    type: z.literal("exhausted_memory_budget"),
    minimum_xact_id: z.number().nullish(),
    read_bytes: z.number(),
    actual_xact_id: z.number().nullish(),
    reason: z.string(),
  }),
  z.object({
    type: z.literal("exhausted_timeout"),
    timeout_ms: z.number(),
  }),
  z.object({
    type: z.literal("on"),
    minimum_xact_id: z.number().nullish(),
    read_bytes: z.number(),
    actual_xact_id: z.number().nullish(),
  }),
]);
export type RealtimeState = z.infer<typeof realtimeStateSchema>;
