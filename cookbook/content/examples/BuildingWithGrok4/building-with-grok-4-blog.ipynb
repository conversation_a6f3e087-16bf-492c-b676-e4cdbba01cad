{"cells": [{"cell_type": "markdown", "id": "62ecf774", "metadata": {}, "source": ["## Building with Grok 4\n", "\n", "This is the accompanying code for the blog post [Building with Grok 4](https://wayde.ai/blog/building-with-grok-4).\n"]}, {"cell_type": "code", "execution_count": 1, "id": "bc9de267", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/development/braintrust/bt-examples/.venv/lib/python3.12/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}, {"data": {"text/plain": ["True"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import base64\n", "import json\n", "import os\n", "from datetime import datetime\n", "from functools import partial\n", "from textwrap import dedent\n", "\n", "import braintrust as bt\n", "import cairosvg\n", "from anthropic import Anthropic\n", "from autoevals import Score\n", "from dotenv import load_dotenv\n", "from IPython.display import SVG, Image, Markdown, display\n", "from openai import OpenAI\n", "\n", "load_dotenv()\n"]}, {"cell_type": "markdown", "id": "ebecffa0", "metadata": {}, "source": ["## Config\n"]}, {"cell_type": "code", "execution_count": 2, "id": "2f2f1830", "metadata": {}, "outputs": [], "source": ["BT_PROJECT_NAME = \"wayde-bt-new-model-benchmark\""]}, {"cell_type": "markdown", "id": "663a7164", "metadata": {}, "source": ["## Setup\n"]}, {"cell_type": "code", "execution_count": null, "id": "a89d1cf2", "metadata": {}, "outputs": [], "source": ["bt_project = bt.projects.create(name=BT_PROJECT_NAME)\n", "\n", "grok_client = OpenAI(api_key=os.getenv(\"XAI_API_KEY\"), base_url=\"https://api.x.ai/v1\")\n", "openai_client = OpenAI(api_key=os.getenv(\"OPENAI_API_KEY\"))\n", "anthropic_client = OpenAI(api_key=os.getenv(\"ANTHROPIC_API_KEY\"), base_url=\"https://api.anthropic.com/v1\")\n"]}, {"cell_type": "code", "execution_count": 4, "id": "a2ec5dcb", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["grok-2-1212\n", "grok-2-vision-1212\n", "grok-3\n", "grok-3-fast\n", "grok-3-mini\n", "grok-3-mini-fast\n", "grok-4-0709\n", "grok-2-image-1212\n"]}], "source": ["for model in grok_client.models.list():\n", "    print(model.id)\n"]}, {"cell_type": "code", "execution_count": 5, "id": "36af3f83", "metadata": {}, "outputs": [], "source": ["wrapped_grok_client = bt.wrap_openai(grok_client)"]}, {"cell_type": "markdown", "id": "863d6995", "metadata": {}, "source": ["## Tasks\n"]}, {"cell_type": "markdown", "id": "303549fb", "metadata": {}, "source": ["### Create SVG Image\n"]}, {"cell_type": "code", "execution_count": 6, "id": "e8c0f777", "metadata": {}, "outputs": [], "source": ["@bt.traced()\n", "def create_svg_image(image_description: str, client, model_name: str, generation_kwargs: dict = {}):\n", "    rsp = client.chat.completions.create(\n", "        model=model_name,\n", "        messages=[{\"role\": \"user\", \"content\": image_description}],\n", "        **generation_kwargs,\n", "    )\n", "\n", "    # Extract svg content - handle both markdown wrapped and plain SVG\n", "    content = rsp.choices[0].message.content  # type: ignore\n", "\n", "    # Remove markdown code blocks if present\n", "    if content.startswith(\"```svg\"):\n", "        content = content.strip(\"```svg\\n\").strip(\"\\n```\")\n", "    elif content.startswith(\"```\"):\n", "        content = content.strip(\"```\\n\").strip(\"\\n```\")\n", "\n", "    # Find SVG content if it's embedded in text\n", "    if \"<svg\" in content:\n", "        start = content.find(\"<svg\")\n", "        end = content.find(\"</svg>\") + 6\n", "        if start != -1 and end != 5:  # end != 5 means </svg> was found\n", "            content = content[start:end]\n", "\n", "    svg_string = content.strip()\n", "\n", "    return svg_string\n"]}, {"cell_type": "code", "execution_count": 7, "id": "736bd2cb", "metadata": {}, "outputs": [{"data": {"image/svg+xml": ["<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"400\" height=\"300\" viewBox=\"0 0 400 300\">\n", "  <!-- Background -->\n", "  <rect width=\"400\" height=\"300\" fill=\"#87CEEB\"/>\n", "\n", "  <!-- Bicycle Wheels -->\n", "  <circle cx=\"100\" cy=\"220\" r=\"40\" fill=\"none\" stroke=\"#000\" stroke-width=\"5\"/>\n", "  <circle cx=\"300\" cy=\"220\" r=\"40\" fill=\"none\" stroke=\"#000\" stroke-width=\"5\"/>\n", "\n", "  <!-- Bicycle Frame -->\n", "  <line x1=\"100\" y1=\"220\" x2=\"200\" y2=\"140\" stroke=\"#FF0000\" stroke-width=\"8\"/>\n", "  <line x1=\"200\" y1=\"140\" x2=\"300\" y2=\"220\" stroke=\"#FF0000\" stroke-width=\"8\"/>\n", "  <line x1=\"150\" y1=\"220\" x2=\"250\" y2=\"220\" stroke=\"#FF0000\" stroke-width=\"8\"/>\n", "  <line x1=\"200\" y1=\"140\" x2=\"200\" y2=\"220\" stroke=\"#FF0000\" stroke-width=\"8\"/>\n", "\n", "  <!-- Seat -->\n", "  <rect x=\"185\" y=\"120\" width=\"30\" height=\"10\" fill=\"#000\"/>\n", "\n", "  <!-- <PERSON><PERSON><PERSON>s -->\n", "  <line x1=\"200\" y1=\"140\" x2=\"230\" y2=\"110\" stroke=\"#000\" stroke-width=\"5\"/>\n", "  <line x1=\"230\" y1=\"110\" x2=\"260\" y2=\"130\" stroke=\"#000\" stroke-width=\"5\"/>\n", "\n", "  <!-- Pedals (simplified) -->\n", "  <circle cx=\"200\" cy=\"220\" r=\"5\" fill=\"#000\"/>\n", "  <line x1=\"200\" y1=\"220\" x2=\"180\" y2=\"240\" stroke=\"#000\" stroke-width=\"3\"/>\n", "  <line x1=\"200\" y1=\"220\" x2=\"220\" y2=\"200\" stroke=\"#000\" stroke-width=\"3\"/>\n", "\n", "  <!-- Pelican Body -->\n", "  <ellipse cx=\"200\" cy=\"100\" rx=\"50\" ry=\"70\" fill=\"#FFF\" stroke=\"#000\" stroke-width=\"3\"/>\n", "\n", "  <!-- P<PERSON>can Head -->\n", "  <circle cx=\"200\" cy=\"40\" r=\"20\" fill=\"#FFF\" stroke=\"#000\" stroke-width=\"3\"/>\n", "\n", "  <!-- <PERSON><PERSON><PERSON> -->\n", "  <path d=\"M200 40 L260 50 L200 60 Z\" fill=\"#FFD700\" stroke=\"#000\" stroke-width=\"2\"/>\n", "  <path d=\"M200 50 L260 50 L200 60\" fill=\"#FFCC00\"/>\n", "\n", "  <!-- Pelican Eye -->\n", "  <circle cx=\"210\" cy=\"35\" r=\"3\" fill=\"#000\"/>\n", "\n", "  <!-- Pelican Wings (simplified) -->\n", "  <path d=\"M150 100 Q120 150 150 200\" fill=\"#FFF\" stroke=\"#000\" stroke-width=\"3\"/>\n", "  <path d=\"M250 100 Q280 150 250 200\" fill=\"#FFF\" stroke=\"#000\" stroke-width=\"3\"/>\n", "  <!-- Wing tips for black feathers -->\n", "  <path d=\"M150 200 L130 220 L150 220 Z\" fill=\"#000\"/>\n", "  <path d=\"M250 200 L270 220 L250 220 Z\" fill=\"#000\"/>\n", "\n", "  <!-- <PERSON><PERSON><PERSON> -->\n", "  <line x1=\"180\" y1=\"160\" x2=\"180\" y2=\"240\" stroke=\"#FFD700\" stroke-width=\"5\"/>\n", "  <line x1=\"220\" y1=\"160\" x2=\"220\" y2=\"200\" stroke=\"#FFD700\" stroke-width=\"5\"/>\n", "  <!-- Feet on pedals -->\n", "  <path d=\"M175 240 L185 240 L180 250 Z\" fill=\"#FFD700\"/>\n", "  <path d=\"M215 200 L225 200 L220 210 Z\" fill=\"#FFD700\"/>\n", "\n", "  <!-- Pelican \"holding\" handlebars with wing -->\n", "  <line x1=\"250\" y1=\"130\" x2=\"240\" y2=\"120\" stroke=\"#000\" stroke-width=\"3\"/>\n", "</svg>"], "text/plain": ["<IPython.core.display.SVG object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["svg_string = create_svg_image(\n", "    \"Generate an SVG of a pelican riding a bicycle\",\n", "    client=wrapped_grok_client,\n", "    model_name=\"grok-4-0709\",\n", "    generation_kwargs={\"max_tokens\": 10000},\n", ")\n", "\n", "display(SVG(data=svg_string))"]}, {"cell_type": "markdown", "id": "4ce82e02", "metadata": {}, "source": ["### Describe Image\n"]}, {"cell_type": "code", "execution_count": null, "id": "2e1922d4", "metadata": {}, "outputs": [], "source": ["@bt.traced()\n", "def describe_image(image_path: str, client, model_name: str, generation_kwargs: dict = {}):\n", "    with open(image_path, \"rb\") as image_file:\n", "        image_data = base64.b64encode(image_file.read()).decode()\n", "\n", "    image_url = f\"data:image/png;base64,{image_data}\"\n", "\n", "    rsp = client.chat.completions.create(\n", "        model=model_name,\n", "        messages=[\n", "            {\n", "                \"role\": \"system\",\n", "                \"content\": \"Describe this image in markdown format. Include the following sections: Simple Description, Main Subject, Background and Setting, Style and Tone\\nUse bullet points for all sections after the Simple Description section.\",\n", "            },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": [\n", "                    {\"type\": \"text\", \"text\": \"Describe this image\"},\n", "                    {\"type\": \"image_url\", \"image_url\": {\"url\": image_url}},\n", "                ],\n", "            },\n", "        ],\n", "        **generation_kwargs,\n", "    )\n", "\n", "    content = rsp.choices[0].message.content  # type: ignore\n", "    return image_url, content"]}, {"cell_type": "code", "execution_count": 9, "id": "4e9d9f8e", "metadata": {}, "outputs": [], "source": ["os.makedirs(\"_temp\", exist_ok=True)\n", "png_data = cairosvg.svg2png(bytestring=svg_string.encode(\"utf-8\"))\n", "with open(\"_temp/created_image.png\", \"wb\") as f:\n", "    f.write(png_data)"]}, {"cell_type": "code", "execution_count": null, "id": "32176632", "metadata": {}, "outputs": [], "source": ["image_url, description = describe_image(\n", "    image_path=\"_temp/created_image.png\",\n", "    client=wrapped_grok_client,\n", "    model_name=\"grok-4-0709\",\n", "    generation_kwargs={\"max_tokens\": 10000},\n", ")"]}, {"cell_type": "code", "execution_count": 11, "id": "242ea80a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["### Simple Description\n", "This image is a simple, cartoonish illustration of a white bird with a yellow beak and legs, stylized to appear as if it's riding a red bicycle. The bird's body is egg-shaped, and its legs merge into the bicycle's frame, creating a whimsical and humorous scene set against a plain blue background.\n", "\n", "### Main Subject\n", "- The central figure is a white, egg-shaped bird resembling a stork or heron, with a long yellow beak pointing to the right.\n", "- The bird is depicted in a riding posture, with its yellow legs extending down to form part of a red bicycle's structure.\n", "- The bicycle has two black-outlined wheels and red frame elements, but no handlebars or seat, emphasizing the bird's integration with the bike.\n", "\n", "### Background and Setting\n", "- The background is a solid, uniform light blue color, providing a clean and minimal canvas.\n", "- There are no additional elements like landscapes, objects, or other characters, suggesting an abstract or isolated setting.\n", "- The simplicity of the background highlights the subject without distractions, evoking a sense of openness or sky.\n", "\n", "### Style and Tone\n", "- The style is minimalist and illustrative, using bold black outlines, flat colors (white, yellow, red, black), and simple shapes without shading or detail.\n", "- It has a cartoonish, vector-art feel, possibly created digitally, with exaggerated proportions for comedic effect.\n", "- The tone is whimsical and playful, conveying humor through the absurd concept of a bird riding a bicycle in an anthropomorphic way.\n"]}], "source": ["print(description)"]}, {"cell_type": "markdown", "id": "c5eb6bca", "metadata": {}, "source": ["### Putting it all together\n"]}, {"cell_type": "code", "execution_count": null, "id": "04252056", "metadata": {}, "outputs": [], "source": ["@bt.traced()\n", "def create_and_describe_image(image_description: str, client, model_name: str, generation_kwargs: dict = {}):\n", "    # Create SVG Image\n", "    svg_string = create_svg_image(\n", "        image_description, client=client, model_name=model_name, generation_kwargs=generation_kwargs\n", "    )\n", "\n", "    # Convert SVG to PNG and save\n", "    os.makedirs(\"_temp\", exist_ok=True)\n", "    png_data = cairosvg.svg2png(bytestring=svg_string.encode(\"utf-8\"))\n", "    with open(\"_temp/created_image.png\", \"wb\") as f:\n", "        f.write(png_data)\n", "\n", "    # Ask model to describe the image it created\n", "    image_url, description = describe_image(\n", "        image_path=\"_temp/created_image.png\", client=client, model_name=model_name, generation_kwargs=generation_kwargs\n", "    )\n", "\n", "    return {\"image_url\": image_url, \"description\": description}"]}, {"cell_type": "code", "execution_count": null, "id": "3d594ce6", "metadata": {}, "outputs": [], "source": ["image_url"]}, {"cell_type": "code", "execution_count": 14, "id": "34a19306", "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAZAAAAEsCAIAAABi1XKVAAAABmJLR0QA/wD/AP+gvaeTAAAgAElEQVR4nO3deXwTZf4H8O/M5E7v+6Cl0IOr5Sggl4AoguCFCAi6qFTFn7rgtaKsru6lKOx6razoKqAoIAqCiFrkEAQKUopAWyhtOXrfR5LmzszvjylpSNI0SZNJJv2+X/wxyUxmntKnnzwz88zzEKsKGgEhhPiA9HUBEELIWRhYCCHewMBCCPEGBhZCiDcwsBBCvIGBhRDiDQwshBBvYGAhhHgDAwshxBsYWAgh3sDAQgjxBgYWQog3MLAQQryBgYUQ4g0MLIQQb2BgIYR4AwMLIcQbGFgIId7AwEII8QYGFkKINzCwEEK8gYGFEOINDCyEEG9gYCGEeAMDCyHEGxhYCCHewMBCCPEGBhZCiDcwsBBCvIGBhRDiDQwshBBvYGAhhHgDAwshxBsYWAgh3sDAQgjxBgYWQog3MLAQQryBgYUQ4g0MLIQQb2BgIYR4AwMLIcQbGFgIId7AwEII8QYGFkKINzCwEEK8gYGFEOINDCyEEG9gYCGEeAMDCyHEGxhYCCHewMBCCPEGBhZCiDcwsBBCvIGBhRDiDQwshBBvYGAhhHgDAwshxBsYWAgh3sDAQgjxBgYW8oDkjqPJHUfdW4uQ8wS+LgAKBDc2rwGAzfJJbqxFyHnYwkK9xTagumtGOV6LkEswsFBvsQ0oywXn1yLkEgws1CuWTSfbZpTjtQi5CgML9YpVu8mllwi5CgMLuc/VJhU2slAvYWAh97lx0QobWag3MLCQm9y7LYiNLNQb2A8LuclBW8lxM+rG5jXYJwu5B1tYyB296dqOjSzkNgws5I5eXorCK1nIPRhYyGW9byJhIwu5BwMLucwj7SNsZCE3YGAh13iqcYSNLOQGDCzkGg+2jLCRhVyFgYVc4NlmETaykKswsJALPN4mwkYWcgkGFnKWNxpE2MhCLsHAQs7yUmsIG1nIecSqgkZflwHxGwGmBE1BcsfROO2ZSH1pkLFeRHcAgJ6UqwRxzaK0OsmIq/IbayXZDIFfkD4j1KkIgtCL5G6s9R8YWMh9Icbq7Jb1wxRfBxtqe9xYKUwoCpl/KjxHKUzgoGzISnjLZQBojRjgxlr/gQ8/I3fITM1TGt7Iat9KMXonPxJsqBnf/N7Ylg/Pht1/OHqlhorwagmRFbmyARimu0hyvNZ/YBMduWxo+/al5RNGtn3ufFqZUYx+VOvGpeUThii+9UbZkF1CnUqk7xAZ1CJ9h6tr/QoGFnIByRhm1T57V83/SUytvdmP1NRyd/XS22r/RDIGT5UNORDU0XnlR65qcHWtX8HAQs4S0pp5VYtHtH3hqR2ObPvs3qoHhbTGUztE3ZErO5NIrqh3da1fwcBCTiEZwz3VSwaq9nt2t6mqffdWLXbj1BI5jz3jY5dtz/scr/U3GFjIKTPrVng8rVgpHYem17/sjT0jlvmMj2V13ud4rb/BwEI9G9b+jQfPBG2Nat04RLHTe/vv48xnfJ0vrz/vc7zW32BgoR5ITS0ctIBm1q2Qmlq8fZQ+yPKMj2V53ud4rR/CwEI9mNrwOgdRIjG1Tm58y9tH6YOszvhY5vM+x2v9EAYWciTYUJPVvpWbY41o+yLEUMXNsfoOqzO+zjevnfc5XuuHMLCQI6NbP7W6hafWw7LPIOYJiHkCln0Galfu7zn+LMXos1s3eqDQ6BrbMz4We97neK33S+cODCzULYKhhym+sXpzxWb4YC80KqBRAR/shRWbXdhhj58dpthGgKl3pUZd7J7xseSqBsdrvVOi3sLAQt1K0J4KNtRYvbn95HUvv/nNhR32+NlgQ22c5owLe0QO2T3j61ylqHe81jsl6i18+Bl1y+7QesT1LylXvvKc+Wx/9a+10mwXdoq6V9V/nMUrRqJpl6kapVqFwKAWGbUAYKKERqFMKw3tkEdppaE2vyK/g4GFuhWv/d32zbljYe3PXS/vGePCDp35bJwWW1geRjB0aFtlaGsFZbJ+cpMy0ZSpXaxtD22tMFHC9vDk9rAkfx62DAMLdStCX2775ur7gSDgq+MAAPeNh7cWubBDZz4bqStzp6yoG3JVQ2RjqcCo63FLymSIaCoPaatuiU5XBUVzUDY34AB+qFvPXMzo5agMbtBQEe9llHB80EAV3nI5vPmyGx9sC+/fEjkQCL87Q8QWFuqWkPbBvW0RreL+oAGIYWLrity+2RfWelVg0DTEDfO3zPLfk1WEkNsimi/1smtCkKohvPmSp8rjKRhYqFsG0gdTEujJIO4PGmDkqoaw1qu9309461W5yr8uGWFgoW6pBLF95KCBhGDoyEaP3biIbLxIMrSn9tZ7GFioWy2iVO4P2ixO4/6ggSS0tUJg1Fq9eeLEiccff/zxxx8/ceKE7UccrBUYdSFtld4qq+swsFC3aiUjnd947c9Q2Wzn/Yrm6/pe9ajOlYMiG0yovXzZsGFDW1tbW1vbhg0bXF0b2lYJwHi+pG7BwELduiq/0ckt1+2HP26E8a/B7oLr3t91Csa9Cn/cCOucHqz0qmyyK2VE15Fq2m17h/YSZdRLtArP7tNt2K0BdatWkq0QJIYYq3vcctEE+OYE7C+Cu/4Nw5PhpiEAAAeL4VwlAMCtWbBoglNHVAj71UlG9KbMfZysm2vkOTk569evZxdcXcvuVisJ9Vwx3YcdR5EjNzX8c3zze85sSTPw4T74+w5osPgyjg2F1+bC47cA6VxvnryoZw5F4/ju7kusPCXWtnt8tzppaHW/0R7frRuwhYUcORWeM7blQ2dmtSEJeOpWWHozHCyGCzUAAEMSYNpQEFDOHstEiArC7H/DIycJDGrv7NZfpmLDwEKOKIUJZ8PuH+X0uHpCCmZkwYwsd471e9iDSmG8O59E15C0V0YTIz19XcxteNEd9eBQ9J81VIS3j6KhIo5Ev+DtoyC+w8BCPdBS4Xvj3vT2UXLj1nAQiwGPJh2dgefm5ra22nmavbW1NTc319FuKWFvS+YheEqIenY+5J7+HUdHtn3mpf0XhD9yIeQuL+28TzEKZZTJ/kX3qqqqzz//fNOmTaNGjcrKyoqOjgaAhoaGwsLCgoICABg2bFi/fv3sftYglHqvzC7Bu4TIKQSY7qnKyVD+4PE9lwfduj3pcxq/Oz0hsqkstLWiu7X19fV79+7Ny8trbu7q4xsZGTlhwoSZM2fGxMR098G28P4tUT547MEWBhZylpDWzKnOSVXt8+A+y4Jm7Er8xED6yxc430k0bQlVBT1u1tLS0tLSAgCRkZHh4eE9bl/db7RO6hf9sPAaFnKWgZRu7/f56fCHPbXDgvBHdiR9hmnlQVppqMmJ600RERFpaWlpaWnOpJVJINJJQjxROg/AwEIuoAlhbtyaXYn/6+UFcjUVuTPx071xb+KZoKcR7eHJnt1jW1iy/wzjh6eEyB1SU8vkxrdGtH3hTJ9SSzoD/O8gvLYdWnBg0Ws8+zdIMHTSleO2Aza4xygQV6ZM8J9pKfylHIhfNFTE3ri31qXmH498WiFIdOYjlc2w6jtIfRaWfYZpZa2+/IKndsUQZEt0uqf21hyT4T9pBdjCQr1HMHSc9vdk5S+Gk+vjhfWJ4RAsoxjapNJCVQtcrIP8S3CgCE5dBtpfBinxL7c8/sLB/7097+//GTV7vqf2GdFcHtbS20FHWyNSWiMHeqQ8noKBhdxEgjFKdzFKdyFadz5Ee0VbdigEWqOCgSAgTAYA0KYGhoEmJdS1Q1UzXG6Ec5VQVAXna8CI09HbIEnKk5nFMDF1RUG9GNa9Izi2Pnao/1y9YmFgIddE6S6kqXKTO4710/zm3gw3Si0cLYFDF+C7U1Dc89A1fQhJCe57/cPhM+Z4aoeBN80XBhZySqihcnjbl0MUO+3Oruq2klr4Kg8++cX+aKV9BEkJaJOxc9mz7SwAuaoxsrHU+WvwRoGkOSa9Q44TqSJ+6qf+bXzze6mq/QR460SOAaos+NbjkcurpWO9dAh/Vrhv95aVS72XWQRDh7ZVhbZVUEZHt3RNAlF7WHJ7WD+/uspuBQMLdStOe+amhn+kdBzi7IiXg6Ydin6lTjKcsyP6CW9nFgAAw0h0CpmqUaptFxg0pMkIADQlMAqlGkmoWh6tlYT44TmgFWr64yt8XQbkd8S04pb6126r/VO4wZ0rIG4L118Z0falzNRcLbvBRIi5PLRvxQwcFDtwUNGBPQxDAwDDMOd/+SmiX//49GG93LPApJeqW+SqxmBlnVzVINGpBEYtwTAkYyKAASAo2igw6sXadqm2TaTvIE0GmhIyDkd98CFsYSFr8ZqCu6sfDzNc8WEZFMJ+3yV8VCW7wYdl4J6n2lkkQ0vVzdKOZpm6RWBwpwepQSjRSCM08ki1PNKvzhCxhYWuk9366T1Vj0rpFt8WQ0wrMhVfq6nIOmkfmvWr9+0ssVYR0VweXXc+WFkn1ilJ2uheSSjaKNYpg1QNoW1VQoPaJBCbBH7R4MXAQl1uavjn1MbXCfCLmX4JoNNUP5OM8aq8D0385XZmSTWtMXXFES2XxToV4blpBAmGFutUIYoaqabVIJAYfT0wFgYW6jSlcdWE5nd9XQprSZrjAESFfJKvC8IdVzNLaNTG1BVHNF8SGHXeK5XAqGVbbVppKE367JF1DCwEADCyddO0hr/6uhT2JauPKkRJ9RK3ZrbgJ+czK1hRG1t9VqTv4KZgQoM6uL3GJBDrxcHcHNEKBhaCKN2FuVUPk+Dm9Q4ODFAdKA2epRb4aW9Gb+g5sxgmsrE0ovmSB08AnUEAI+9oImmDRhbBfTcIDKy+jmDoBZX3hxirfF0QR0gwxWvPnAn7A4C/dxTyIEeZxTAxDRdCFDW+KptEqxAYdWp5FMeZhYHV1w1TfjO69VNfl6JnwcbaZnF6k3iIrwvCqe4ya2iEJLTdx98xYp0KCEIr7XnMUg/yox4WyCcmNL3v6yI4a0KT390T4EDm9DsXrfqYpDqvc9O0qWjLf8Naezt0jEeEN1+WaNq4PCIGVp+WoD0VpfPY0HHeFqMrjtec9nUpfMAyswiCyMlZ4j8nxlGNF7k8HAZWn5au/MnXRXBNmsrRfJ8BzJxZo0aN6m72QJ8Q6VTSDu6G2sApAPq0BE2+r4vgmkTNSV8XwWcyp9+5CD4Or+h5Fi+OBakaNPJIbo6FgdWneXZwKw5E6C/5ugi+lDn9zthiv5sVTaK1P9e0N+ApYZ8mNbb6ugiukRmbfF0EH5OK/K6RQbn1fLV7MLAQn/jVyAGoE4ddsfDX36dpBL2aD5V7aoqjayV+y09GTbDEZZEwsPq0FmGqr4vgmlaRf806xT2dNMzXRbCmlYRydiwMrD6tWsazMdT72pB+tlRBMb4ugjVVcCxnx8LA6tMuSm/2dRFcUyKe5usi+JhaFqEXB/m6FF1MpFAj4+7CAgZW32Uy6Fe/8p9Cv37q+TpnKmD1S2uMei+O+sQDBNEcneHrQlggABjuhovAwOqjTAb9ly/knD+cu2qXr4vitDd2wcW8g5ueXdzHM0sjDWuNHODrUnSiTAaxVsHZ4TCw+iJzWgHAljw4yYfOmCfK4OsTAICZBQDQGp6iDIn3bRkMIhm7IO/grnMcBlafY5lWAMAw8PA6UDuaYdP31HpY8nHXmUf5iT6fWQTRGDO4PSzJV8dvC0tqjkpnl+WqBs6O63e9ZpFXWaUVq7galm2ET5f6qlA9e3I9nK/uXJ4/DlbdBzf98+CmZxcvfmeTQOR3/ZI4QhDN0el6SXBEfUl7c6NCodBqtYzXLieRJCmRSMLDw+Wh4c0xg1QhcQTD0ARFMiahQSMyqPVCmZcOfV0xODgG8hN204p1RD/tcMRz3BfJGb9EvvgbMRMAokNg23LYthxSY+G9B/HcEABAIQgqq6ypr6/XaDTeSysAoGlarVZXV1eXNytU8igAYAhCI++8PyhWczQqFk6k2lc4SKuMCdPYpsqUxlUTm97mvmwOHI3606/RL7KFnyrM/TCna9Wct2HXqa7C+66MvkPT+sPbGEUzJQtKiAyXyWQk6a0mCE3TCoWivqmZNujJ+IHCsbMAIKy1IqKpDACUoQmNMYO9dGhL2MLqE5xJKwA4HL0yN24NTQg5L6AdJkL0U9zbv0a/CACUUPTAmvW/GmccL+va4IOHIUjSp9tZpiuFjKKZCApPSRkQFBREkmRbeLIiJN6D03CZSIEiNKEtPJkkybCwsKSUgSAU07WX6LpLAKCVhLCbiXVKTx3RMRzTPfA5mVasOunI8qBbUjp+ldCcDn1rpV2Y9E3Sl6XBs8zvkBQ17JY7t319/N7BlRQJABAiBZqBg8XQXHWluuh01q13mccR7iNMhb8y2o7Q9BFhjAYAjJSoPmG4OiimPTxJLwlhSIGxQyEi3Xky2SiUqoJjWyMHNMcMUgdFa6XhwYpakjYJSUIbHKNvqQe9jkoaxFACdrBmwmRsC+/PwVPQGFgBzqW0YqkEcefCFopodbz2DMdTSAEADYJTEY/s6vdpq8i6qxFJUfET7y375dvsuM4wHTMQPv8VFJo+mlnGoiNA00kxkRRDA0BLVKpOGgoAQBAGkUwtj+qIHvjb2aIdH7135crl5uZmtVqt0+klQUGEQEgyNACYSAFDUkahVC8K0srCO4Jj28OSm6Mz2iL6q+VRBpGsM4MIgiFImboZAKQU0drUwOi1gvTRDEGGKKpJ2kQAowpN4GCC1T702+2D3Egrlo4M+Tlu1dmwB6Y2/GNgxwEvF7NLedD0Q9GvNEi6nZadEopa781tPJcVLdMBgEwEry+Ah9YBXOuf1beuZxkNACCkDQBgpETK0ATbTdKn3aEzMVtWLqVNnfNOkiQ17+//GTV7vkuHUoYmhrVeFRh1IjABABg7O8IYBFLKqAcAyqgzCCTu/yzOwRZWwHI7rcw6BDFFofMvBd0sM7VE6C97r7VFg6A0eNb3CR+ciFzWIejh4V6GkppE4enqn9mXWcmwKx/q2wGgz7WzTCUnASA6Ohosm1c2nJ9H2hGLRlZTUxMACAbdAAAydQs777RaFmkQy3v5E/UIAysw9T6tzJTChPMh95wNu19PhsiNjTKTJ2ccaBIPOhXx2J6ED86EL1YJne263SAbnqH4Xm5qAgCCgIgg+Oa3zlV9KrPMgWWkRI1xQx1cQvJIZunFweyVLMvAkmpa2SvuWlm47to1eO/Bbg0ByINpZStSfzFd+VOSOq+f+oSYdufekI4MrpKNq5RNLA2+rVmU7l4xUlU/z6+8n12mGRi2Ai5YzILcF/o6EAyj3f1fABgyZMiGDRt++omLCZBuu+22JUuWnD9/HgAkdz7BEGRkU1loawUANEeltYcne7sAgf8t1Nf0Pq3EtPKOmidPRjxRIZtou7ZZlNEcmXE8cjkBpkhdaZS+JEZbHGqoCDbUyI2NclMTACMxtQEQWiqUAVJNRXYIopWCxHZRUoNkaJNocLM4jQHKQQGS1cfGtnz4fcJ/dWRwd9uUB91aJxkRpz0DACQBL90FD6/rWtsXrmcFt1ezQ6m3trYeOMDRdcb9+/ffdddd1wpQowjrR0Nns47k5P4MBlZA8UjbanTL/9KVP6Urf6qQTTwc8+cq6Ti7mzFANYkHN4kHXwi+u7flvqaf5sSUhjeS1cfYYhyLctT5Pi/qmXuqlrDL90+ElVuh1qInRmBnFsEwYa1X2ZOjnTt36vUcPQtqMBi+++678ePHA0BYyxW7l/m9CjuOBg6PpJWYVt7Q0tlWSVYfS+446uFSOtS/4wibVgBwQ8t/JSZH80ddDJrdLOocGUpIQc5NNhsEbp/S4PZqgbHz5+KsecXav38/uyAw6YPba8wNK27mB8EWVoDw1HWrMS0fS0ydc39pqdCC8Ec8Wcqe5EcsHdvyEVsAiak9u/VTB40shiALIpbcWreSffnULMmb32lN9HXbBGQ7i21emV/+7Xi1g429QffdWnYhrOWKJiiKXaY5CSxsYQUCT6WVmFaObfnI/PK3iCe1FHfzCwCAjgw+GfG4+WWPjazC0AUGsnOQgPgg7aP3Z9tuE3jtLMvmlW8JTHrRtdH7TJSIgyNiYPGeB+8J+rZ5xcqPWKqlwtlltpHlYGMdGVIcco/55VMLBg6ZMtN2s0DKLKvmlc+JdB3sgkmAgYV64sG08nnziuV6I+s+83K6et/i1R8HdmaZm1dGSgRsRnA48TLAtT7ulNAoEAOAuTsxN7MTYmDxmGf7W/lD84rlUiOrSjqug4q5tnHbQP2JB9asD9TMsmxetUWkEGExAGCqLnP4IQ+jG6sAgAyJbAvv3/UmQRooDCzUPc+mlZ80r1guNbIYgiwNvs38Mk21lx2LJiAzy7J5pQxNEAzIBADj+eOMqpWjEhh0xuJjAEAmZShDE01U50hENCXkZsJ6DCxe8nhfdv9pXrFcamSVBc8wL/fTnIBr42cFWGZZNa8YgiTjU8n4gWDQ6X/dYbpaBAZv/lBGPV1brj/8NdPRToRGU8lDGYIwz/lMmgwEQzvegUfgozn84/G0EtPKJ8pGmwPrcPRKxz02uTGp6d+TG99kl7VU6LrUU901+iSmtqdLB7F/MAxQ7w66qCNDwMuPKHFPYNTG1J+XqFuNAnFlyoTOfk8mo6HgZ7qWu4mPiNBo4bjbCYkcAGJrzrJT5hhE8trEEUbvj9aALSye8cYfob81r1jON7K0VFiTaBC7TIApQXOKXQ6wdpZRIKlJHFWTOKo5OqOrlyYlEI6dJRgzk4hKBKE385cSEuFxguFTRJPvZdOKYGipurPa1MVncZBWgB1H+cUbaeVXV68ssVeyzI2sG1r+WxD+SHdlq5aOjdadZ5ejdRcuyztntGczy+5/Gk/7lGpl4bZvUglpVEIaxyWRqltIxgQABpHcPEeht2ELize8dILjn80rlvONrBZRqnk5XH/ZclWAtbP8h3n+1I6gaM4OioHFD15KK79tXrGcv13YKhpoXg7Xl1utxczyPIaRqa4FljySs8NiYPGA9y4e+3PziuVkI6tVlGJeDjHW2G6AmeVZEk0bZdIDgJES6cReH7fPDAPL33kvrfy8ecVyspGlI7tKLqQ77O4KM8uDQtqr2AV1cAw3PbBYGFh+zas35v2/ecVyppGlp4LMyyKTqrtdYWZ5hNColV87H1SEJnJ5aAws/+XVtOJF84rlTCPLQErNy0Ja42BvmFm9F9xWxT5CqJFF6EVen3jCEnZrsNZaXVF/6ULjlbLGq2VNV8s17a16jVqjbNdr1AAgksqkwaEiqUwWFhGVnBrVPzU6JS124ODwRA+PZu3tTo98aV6xehwnyzKkzAPOdMcnfR38pF71HsnQIYpadrktLInjo2NPdwAARUNt2YlD5SePlJ880l7vznBoYXH9Bo69MXXsjWnjpoZEx/WyPN5OK//s2u6Y447vwYaap8pGsMtKYcLatDM97pCDfvD+Vq88wtzh3iCSVfYfz/HR+3RgGXTaC4f3Fnz/1cWj+2na5JF9EiSZPHxM9h33jZh5j1je7RwKDnDwh+T8Uy/+w3HIxuiKcy5NZZebxIM+GXjEmX166b/aP+uVZ0nUrSRjUsujOD5uHw2sxiulhza+f3bvLoPW0fWO3hBKpMNnzJn68LLoFBdmsuIgrfjYvGI5yNnBiu/mVHee1ZYHTf86aYuT+/Tsf7jf1quA0ecCq+bCuYPr3yk6sIeh7T9cLpRIkzKzo1PSo1PSolPSQ2PiRVK5JDhEJJMDw7DXHQwadXtDbeOV0sYrZY1XSisLC7qroARJZt5yx7ScZ+MHZfZYNm4e1uVj84rlIGotf6jfIp44EPt353frkf92f65XgaQPBZayqX7P26+ezf2WYexMoJacNSZj4s2pN0xOysymhK4N9moy6CvOnbp08kjJ0f2VhadsNyAIYsSse2c/+7fgyG7nYecmrfjbvGJ1l7b3VC0ZpPyeff+n+H/9HvaQS7vtzX++n9erANMnAouh6ZM7v/jx3b9pVQqrVaGxiSNn3TtmzgNRyQPtftZVbbWVZ3K/PbljU3PVFatVkqCQ6f/34oSFj5Ck9TSinA2Ewt/mFctu4BJgevriEPObGwYcrJe43O5w41fg//Uq8AR+YLVUX93y0mNVRaet3k8cMmLao88OnTqLID3fGY2h6aKDPxz89O2aC+esViVljl705v/CE7ruB3OWVnxvXrFsMzfCUPbg5c5BR9VU5H/Si92bI8+lX4T/16uARE1/fIWvy+BFhft2b1y+qKX6ullGEoeMmPfX92c981rMgAzCO08VEAQRMzDjhrkPJmVmN1y+qGxqMK9SNNSe2r01OiU1ZkAGcDvI3PjmD1JV+9hlLRW6O2GdkeRiDCPPapBkjWr7XMBoAUDA6PSkPE57NlFzkl1bGjyrJOQu9/ZMUlTmLXfUXixqumr9+HRz1ZXqotNZt95FUgLgQ70KVAEbWLTJ+P2/Xv7hndcsOy5LgkNnP/e3e175d1RyqoPPegpBEFHJA8fe8wd5WGTFmZPmkhj1unM/79Io2weMnrh5xSPcpJWYVt5dvZT9OweAY1HPXQ6a5qmdc8lEiClG31/d2XEhTnc2RnteyHRenD4W9WyTeIjbO+8xs4bdPPuHd17z83qVNn4qwcm0ptwLzFNCg067deXS4l9+tHwz85Y77n5pdVAkd2P3WFI21e9ctaL44A+WbwZHxSqb6m039sYAvny/emXJ6tzWTEuFfZBeaCR6+//moNkbFBGlammyfMcP69Wwm+9Y+MY6fg1M6KQADCytsv3zZxdfLsgzvyMQiW5b/tqk+5c6uQeNou1yQV5d2fnGK6VNV8o62lu0SoXlIxSysIio/qnRKenx6UMHZE+QBDv7l//bjs93r15p1OsdbOONtAqMq1eWLPPX7FT4oz/HrfLI/h1klpk/16sBoyc++M4mSRB3A79wI9ACS6ts/3jpnNqSQvM74YnJf1izIWHw8B4/23S1/PSebSVH99eWFDrfQZkkqYTBWXNmyxoAABsWSURBVINunD5y9nxnbglVnz/zxfMPt9VV2V3rpckRAql5xbJtZDEE+emAw03iQZ46hOPMCktIWvyvjf5Vr/60pK220vxOwuCsxz7eGWCZFVCBZdBpNzy1wLJtFZs6OGfttpCYeAefok3GMz/tOL5tQ8W5fPYdsVg8fvz48ePHZ2RkDB48OC4uLjw8XC6XA0BHR0dra2tdXd2FCxcuXrx4/Pjx48eP63SdFxGSh48dv2DJiJn3sJdm7TIZ9J89/UDp8V9sV3kprQKvecWa3PTWpMZ/mV+WBN/5bb/1nj2Eg8waOHrikrXbHPyyOK5XAKBsbtjwx/ssv62Th499dN12oUTq4FP8EjiBRdOmzSseLTrwvfmd5KwxD73/pSw0otuPmIz5uzYf2vA+e7snJCRk3rx5ixYtmjRpklTq7O9Yo9EcOXJky5Yt27dvVygUABDZL2XqkqdH373Ipf5W8vCoFd/ni6SeH6wj8JpXrGkNfxvX/IH5ZX7E0n2xr3v8KAadds2dY1y61Mh9vTLTKts/e+YPV04fN78zZMrMP/x7o+Ok45HACazdq1ce2/qJ+eWA7AlL1m4Tiru9bX/1zMldq16ovVgEAIMHD16xYsXChQudr0+2NBrNli1bVq9eXVJSAgAJg7PuXrk6OWuMeYMeL4tMun/pHX/y8J9coDavonXnH748nWK6rtp4KYut6pUV28zivl5ZMei065+cb5lZ3qhXvhIg3RoK9+3+4Z3XzC/j0obkrN0mlgfZ3dio1+1e8/KuVS8omxtSU1PXrVu3du3a7OxsoVDYmzIIhcJRo0Y9+eSTQ4YMKSgoqCwrOfXdFnVbS+rYKSRFOXMRt/Lcqfj0oZ7tRxMYfa+siOiOhRXz5aYGyzfZPlmVsgkePJBVvbJl2T/LJ/XKdntKIMiafmfJsf2q5s7/H2/UK18JhMBqqb66cfkic2+U8MTkpf/bJQ+3P/BF09Xy9X9ccOFwrlgsfvnllzdv3jxy5EgPdvMjCCIzM3Pp0qUEQZw4fvzKmZMlR/YNyJ7w7T+es38pZMwkmmZ0157tuJj3y4gZc6QhnmkmBEzfq+sxd9b8X7I6z3ZFrO7c72EPeSqRretVQlLSsOwWmydj2MyKH5S5cfkijutV2g1TZKF2pikUiMRDpt5WuP97rXfqlQ/x/pSQpk0fPjTL/ISEQCR6YuOP3d27uZR/dNNzD2pVioyMjG3bto0YMcKrZTt9+vSCBQvKysoogdBkNNhuwJ5Q1JWd/yjndvM96aTM0f+3cY9HngsLyKtXkxvfmtTUda39XNjCdGWux8957dar2NTB3TWTSUpAm4wc1ytpcOjidzYNyLbfqKwq/t1L9cqHeN8dNm/rp5bPc93+3D+6S6uig3s2LLtPq1LMnz8/Pz/f27UKAEaNGpWXlxcfH+8grQQicb+hI2c9/Vfz+5WFp058s7H3R+fRqO3OG9W6wTKt6iWZuXFrnJy70CV265WD8eBpkzEuLu7IkSPc1KtTp07NnTtXo2xf/9R8q16jZl6qV77F78BSNtX//GFX78HMW+4YvyDH7pZFB/dsfuERo063fPnyrVu3BgdzMWajXq/Pycmpra21XWV1sXbioseGTbvdvHbv2lWq5t62fPk1arszYnTFM+pfMr/UUBE7+n1uJCTOTxDtJAf1ykFm1dXVLV68WKvV9vLozggJCfn666+feuopo0735Qs53WWWN+qVb/H7Gta3/3y++nzn6N1iefCS/2wVy+xcaL+Uf/SLPz1kMhpeffXVt956y0sPplrR6/Xz5s3bvXu37Sq7t8MHjpl0cucX7BUTo16namkcdvPttp91HkNQYforoYZKCJSrVx2CaBMhTuk4DAAGUrot+atG8RAAMBFikjGwTxdWyCaeDX9AIezXmwM5rlcOnjcsLy/Pz8+fN2+eQOD1bgQEQcyePdtkMh06dKj40I8DsieEx9sZqsHj9cq3eBxYtSWFu1evNL+8/fm/p46dbLtZ09XyTx6/R69RL1u2bPXq1dyUzUFaUQLBH97eFBIVa/W+SCYXiiUXjx1gX9aXFQ+ZOjPYZjPnKYT9zoUtqpDfKKHbDsW8aur1E3b+oEo2zkhKktV5O5I+r5DdaH6/QZIZpS/Jjf/3kegVvUwrZ+oVSVFx6UN/27GJYazHF+UyswDg5ptvbmpqOpGXV3zwh8xb75KFhFlt4PF65Vs8PiU88Onb5jEeE4eMGDfvYdttjHrd5pce1XYo582b9+6773JTMAdpFRsbazIav/7Lk3YfJ5yw8BHziLcMwxza+J/eF6ZCNnF7v0060vfTFnjK8cjl6wccvCS/2fJNHRm8vd+mCtnE3u/fyXr11StP0CZjfLydhyhyc3PnzJnDzbkhALz//vvs9awtLz7KWb3yFb4GVuOV0qIDe8wvZ/zxZbu3P/a8/WptSWFGRsb69etJLwyoZstBWs2cOfPs2bNpaWnV58/8+K6d3j0kSc14suu7vXDf7sYrZV4sK2958IFBK67Wq3Pnzt155522G3CZWSRJbtiwoY/UK74G1qGN75tH+08cMiJjgp0LNFfPnDzx9QaxWLxt2zbOrrI7SKudO3fGxMR89dVXYrE4b9t6u6N0D7rxVvOXIU2bDn/G4y9DPnK1XkVGRn7zzTc+z6yQkJA+Uq94GVi6DuXZvbvML6c9+qztNjRt2vXmCoZhXnrpJQ7uNIMTaSWRSAAgOzv7+eefZ2h6xz+eo01Gqy0JgpiW0/XjnN27U9eh8mqxkZl79UokEvlDZvWResXLwCrc/715+qPwxOShU2fZbpO/88vaksLU1NQXX3yRgyI5mVasl19+OSUlpa60uGD3V7bbZ95yR9i12z16jbro4B7bbZA3uF2v/CSz+kK94mVgFXzf9fvIvn2B7Wj/tMl4aMP7ALBq1arePHfqJJfSCgBkMtkbb7wBAL9seM92gCSCJEfNvtf88vSer71QZGRHb+qVP2RWX6hX/Aus9voa84hXBEGMnD3fdpszud+2VF8dPHjwvffea7vWs1xNK9aCBQvS09ObKy+fzd1pu3bU7feZl8tP/mp3bBPkWb2vV/6QWQFfr/gXWOW/HTZfFk3KHG13MMbj2zYAwIsvvujtO4PupRUAUBTFnlMc/9rOmHPRKWn9ho1ilxmaLvvtsOeKjOzzSL3yeWYFfL3iYWCdPGJezph0i+0GTRWXKs/lh4SELFiwwKslcTutWIsWLQoODr76+29NFZds12ZM7OpndMniR0Ze4ql65fPMCux6xb/AupR/1LycOvZG2w1O79nGMMz8+fNlMpn3itHLtAIAmUw2d+5cADjz0w7btaljun60shN8+CakTXRNmeHUXv2BL3V7PtLt+Uh/4EvDqb2mmjJweiBzH/JgvfJtZgVavboezwKrpfqqefoGoUSalJltu03JkX0AsHDhQu8Vo/dpxWILyRbYSvKIG8zDpbbVVbXWVNpu4z/o2kv6A5sN+bl0dSmjagOTEUxGRtVGV5ca83P1BzbTtdaP3fkVj9cr32ZWwNQrWzwLrIZLJeblpMxsSiiy2kCrbK8tKRSLxZMmTfJSGTyVVgAwZcoUsVhcXfy7Vmk9IopAJDJfboDrf3A/wxiLjhpO/sioFd1uoVYYTv5kLD4G1x558TfeqFc+zKyAqFf28SywLB8psDvk6+WCPJo2jR8/3ku9GTyYVgAgk8luuOEGmjZd+f2E7drolHTzcuNVP32Wwlh0zFT+uzNbmspOG88f73k7X/BSvfJVZgVAveoO3wLL4v83qn+a7Qbs4P/jx4/3xtE9m1Ystqhssa1Ep3T9gLYjmfgDuvaSk2nFMpUV0LV2rgT7nPfqla8yi9f1ygGeBVazxY0Py/93M7bmDR482OOH9kZaAUBGRgZ0U28s/3L88ZuQNhmLjva82fUMRUf88Bq8V+uVTzKLx/XKIZ4Flrqtxbwcam96VPY3xP62PMhLaQXX/gbsPj0fFpdoXrb8wf0EXXfZwXWrbqmVdN0Vz5emd7xdr7jPLP7WK8d4Nr2i1uKJTbuzeKlamgAgMTHRdpXbjEbjnDlzfvzxR7trc3Nze3+9rLLw1MrsaAcb1JUWO96Ae/PmzcvMzHTjg6fWr96xw84ddz/x7oIp3a3y0p2c3NzcuXPnfvfddx4c8y8hIQEAVK12BkS2HD1Vp+bZI9A8a2HpLf5/RfZGQ2Y38OxgMgKBYOrUqR7cYWCwO3YdADQ0NNh934z9W0KWxo0b59kRSkNCQgBAb29IBpHFN73dDfwZzwJLp+4wL4tldmZ1ZzcICrI/harbXnzxxTfffLPn7foSu98KRUVF69at27fPTg8gM/ZvCZn99a9/fe01R9O1uoH97dhtQGELq0/gZpgaXisqKtq+fTtN00eOHHGQWYy/9sbyFY+nVQDjWWBZtqosW1tWG6hUPPve4COlUmn5srCwkE0r9uWxY8e6Oze0+iDyBoVCAdc3pswsW1V2N/BnPAssy+tWervNXXkQXPttIa+qqakxLxcVFe3YscOcViRJzpkzJyYmpscPIi9hvxVE9m5MWV63sruBP+PZXUKJxf+v3TFe5eFRrTWV1dXVKSkpHj+6N85lfv311ylTpiRljn7y85+sVtWVFr93X+fF/rj0oU9/dcjjR+8NU02ZMT8XLM4E2ffZtBo+3P782wAwOmfF2Fc+6m6tT7y3YEpd2Xl2+Zlth2PThlhtsHbxjKqi00eOHPHeI1+eVV1dDQBB4XbuLGMLizuysAjzclu9nS/qqP6pAHDx4kXuytQ7JSUl0E1nxba6avOyPCySuzI5h4ofSMhDXU0rQhZCxqVwVESnYb3iC54FVqTFsGpN9jrpRvdPg2u/LV5g/wbYvwcrTdc9L2JnAx8jyAstapfSCgAEmTeCvYmzfAvrFV/wLLCiLZ8qsNeLNz5jGAAcP+6nD9naysvLg2vFtmL5A9r9qvStcz9/99Xq11xKKyotm4wbwEnpXIP1ii/4FlgW/78Nl+20zwdkTyBJKi8vT61Wc1guN6nV6t9++42kBAOyJ9iubbxSal62+0SuD537+butf37cPJ2Uk2klGOKVh9J7D+sVX/AssGIHdj19WlV02nZibklwaMLgLL1ef+QID4Z//eWXX/R6feKQEWK5dSdMo15XVXTa/DI21VtzHbvBJq2oec+9NmLC5O62J+ShwhtmC4ZOAILgqoyuwXrFF356l9DqublVBZ2PRIUnJofFJ7XVVgKAQav5y/hEy7WsquLfAWDmzJnsS3/upvjVV18BwKAbp9uuqjhz0qDrfCA2LD4pLK4fpyXrnm1azf/7ByNnzwOGpmsvm+ouMW2NjFYFAIQkiAiLpuIGkvEDgPDrr0arelVZeMq2bTLoxulVxb9v3bp1xowZviijC/hYr5zk19XIroFj+HFfuUdqtXrHjh0EQYycPc92bfnJX83LaeO6fRyXY92mFQAQJJmQKsy+VXTz/eLZS8Wzl4puvl+YfSuZkOrnacWyrFd2Z2cYdfsCgiC2b9/u52eFfKxXzvPTmrSqoNHyn+UqywkCkjJHW61lP5s8fCwArF+/3p+bV1u2bFGpVP1H3BDZL8V27cVjB8zLqWO7PdvikqO04j/LelVydL/tBpFJA5KyxigUCrb94rd4V69c4qeB5UDaDVPMU/JWFp6yO5fR+AVLAOCtt94ymfxurDiWyWR66623AGD8ghzbtY1XStkTWwAgSSrtBt9/EwZ2WgHWK57gX2CFxMQPGD3R/PL3H+zMuD1i5j2R/VJKSkq++eYbDovmgq1bt5aWlkYlD8yacbft2tN7tpmXU8dNCYr08UhYAZ9WgPWKJ/gXWACQbTHjdsGebeYJe81ISjB1ydMAsHLlSo1Gw2nhnNDR0fHnP/8ZAG7KeZa06UXJ0PTpH7abX47ydS70hbRiYb3yf7wMrKzpd4quDdvQWl1R/MsPttuMvntRwuCsy5cvv/HGG9yWrmf//Oc/KyoqEoeMGHXHfNu15/bvZm9XAYBIKhs27XZuS3d9YfpMWgHWKz7gZWCJZPKs6XeZXx745B3bi+skSd29cjVBkmvWrDl9+jT4jfz8/LfffpsgyTl/XmPna5Bhfvn0HfPL4TPvEdkbp5AbfSqtAOsVH/AysABg6sPLzZdIay6ctbz3YZacNWbCghydTrdgwQI/GXCmvb39vvvu0+v1Exc+ZjmfpdmFX/eap2YiSWrqQ8u4LWCXvpZWLKxXfo6vgRWdkpZ1S9ccJHvXvk7bmzxq1jN/Sxg8vKysbMmSJbTNJQmOmUymhx566NKlS/2GjZr19Ku2G9Am4961XScaWTPu9tWzqfVl569LK0pw3xvrAj6tAOuV3/NiYK3Mjrb858G1rJseeZa49qhHzYVzx7dtsN1GIBLd/9Yn0uDQHTt2LF++vNc/U68sW7Zs165dstDw+9/8xHYydAA4tvWTutJidpkgiJtynuG2gF1i04bMeGolu0yS1Py//Wf4jDm+KgzHsF75M762sAAgPmPYyFld3/l7176haKyz3SwyacDidzYJxOK1a9f+5S9/4bCA13nllVc+/PBDoViy+J1N4YnJthsomxv2f7TG/DL7zvvibIaR49LUh5fftvwvfeRM0BLWK39G2PYU5xFlc8Pbcydqle3sy6HTZv/hXxsJe0/YFh/84csXcmja9NRTT73//vskyV1Sm0ymZcuWffjhhyQleGDN+qE3zbLdhmGYL55/qPiXzqkPpSFhz+3IC4qI4qyQ3akvvxCb6vlptP0c1iu/RU1/fIWvy+A+sUwuksouXnuQovFKqSwsIikz23bL6AHp8elDiw/9eCIvr7CwcNasWWKxmIMStre3L1q06IsvvhCKJfe/9YndWgUARzd/dGzLx+aXtz/3dz95ZJLXldttWK/8Fo9PCVnjFyxJyhxtfvnju6+ZHz6wMnTa7Jy1X7PXHUaPHl1QUODtsuXn52dnZ+/atUsaEpbz4Tfd1arKwoKf3v+H+WXy8LHj7n3I22VDjmG98k+8DyySpBa9+T9JcCj70qjXf/lCjt2LDgAwIHvCU1/uSxwyoqysbOLEia+88oqXnrxXq9UrV66cNGkSe+9m2Zf7U0aOs7uloqF284pHTIbOAZhkoeGLVn1EcHhmgezCeuWf+H1KyJIGh8alDT6b+y37UqtSlOYdHD7zHqFEaruxLCQs+86Fug7l1bP5hw8f3rx5c2xs7NChQz119cFkMm3ZsuXee+/ds2cPzTCTFi1dtOojyzkOLGlVik+fmNd0tZx9SRDEwtfXJWWN8UhJUC9hvfJDgRBYABDVP1WjbK88d4p92dHaVHH25Ijb5lICOyMUkhQ1aNItGRNvrj5/trKsZPv27Vu2bJHJZIMGDRKJ7NwVdlJHR8emTZsWLVr0ySeftLe39xs6cvG/Pxsz5wGSsj/ngkGn3fjH+yyHf5y8+MmJi5a6XQDkcViv/A2/7xJaomnTlpceK9y32/xOctaYh97/UhZq/1uI/UjB7q9+Wf9uc+VlAAgODp47d+7ChQsnT54slzv74EJHR8fhw4e3bt26Y8cOdsbpqOSBN+U8m33HAgctcK2y/bNn/nDldNekBkNvmvXAvzbYPlSBfAvrlV8JnMACAJNBv3H5/WUnuiYcjRk4KGftttDYBAefomnT2Z++Pf7NhoozJ9lnx0Qi0bhx48aPHz9o0KBBgwYlJCSEhoYGBwcDgFKpbG9vr66uLikpuXjxYl5e3okTJwwGAwAQBJE8YuyEBY9kzbjbcf1QNtVv+ON95kclAGDgmElLPvhKIOLiBhNyFdYr/xFQgQUAWpXif0vn1Fw4Z34nLD7pgTXr+w0d2eNnm6uu/L7n65Kj+6qLz9h9IMMukhL0Gzpy0I3TR86eF5HYv8ftKwsLvnwhp72+azLLhMHDH/v4W0lQiJNHRNzDeuUniHdb3vV1GTxM16Fbv3h9yS9dc15SQmr2n2ffvPxmu33/bGkV2vJj5TXFNQ2lDfWl9R0tHZp2ja5DBwBiuVgaKpVHyGMzYmPSYhKGJqROSpUES5ws27HPjm1/cbtJ31Vr0yalPfrlo5IQZ/eAfAXrlT8IwMACAIPOsOmxTWe/P2v55vA7hs/717yQGN984bTXtX/9/NeFPxZavjnizhGLP14sEPvp3EXICtYrn6Nue/E2X5fB8ygBNeKuEVqltuJUhfnN+ov1eZ/niaSipFFJBMndBHm0kT780eEND2+oKaoxv0kQxLSnpi14ZwElDJCroX0B1iufC8wWltnZ789uWbZF037daLaJWYm3v3z7kFuHONmSdxvDMMW5xXte32NZpQBAFiZb9MGirNlZXj068h6sV74S4IEFAM1Xmz9/9POrp65avZ+YlTjj+RnD7xjujW9FhmbO7D7z879/ri6stlqVMjblwU8ejEjq9qY44gWsVz4R+IEFAAzN5G/L3/nKzo6WDqtVYQlhw+8cPu6BcYmZiR45Vn1p/ekdp/O35TddbrJaJQ2V3vbibZMfm0xSvH9CAgHWK1/oE4HFUjYqv3v1u/xt+XZnV00elTzk1iHpk9NTxqS4erXSoDNc+e1K6a+l5/edr/y90nYDgiDGLhx711/vCooOcrP0yF9hveJSHwosVk1Rzc9v//z7rt8Z2v6k0EKJsP/o/jHpMTFpMTHpMaHxoZIgiTRUKpaLAUDXodO0a7QqbXtNe31pfWNZY93FusrTlQatwe7eSIocOWfkrc/eGj803os/FfI1rFfc6HOBxWosb9z37r7T357Wq/VeOoRIJsqem33L07dEp/JvukrkHqxX3tZHA4tl0BqKcotObj15fv952uiZqQQIkkgZmzJ24djR944WBwXOIxHIeVivvKdPB5aZokFReqj04uGLpb+WtlS0uLGHiOSI9MnpGVMy0qem+6oPIfI3WK88DgPLWktlS31JfX1pfUNpQ2N5o7pVrVVprR6hkARJZOGy6NTomPSY2PTY2EGxAX87GfUS1iuPwMBCCPFG4HfcQAgFDAwshBBvYGAhhHgDAwshxBsYWAgh3sDAQgjxBgYWQog3MLAQQryBgYUQ4g0MLIQQb2BgIYR4AwMLIcQbGFgIId7AwEII8QYGFkKINzCwEEK8gYGFEOINDCyEEG9gYCGEeAMDCyHEGxhYCCHewMBCCPEGBhZCiDcwsBBCvIGBhRDiDQwshBBvYGAhhHgDAwshxBsYWAgh3sDAQgjxBgYWQog3MLAQQryBgYUQ4g0MLIQQb2BgIYR4AwMLIcQbGFgIId74f0H3c5XR2xkHAAAAAElFTkSuQmCC", "text/plain": ["<IPython.core.display.Image object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/markdown": ["### Simple Description\n", "A simple cartoon illustration of an orange cat riding a bicycle while towing a gray cat in a small wagon against a basic outdoor backdrop.\n", "\n", "### Main Subject\n", "- The primary figure is a smiling orange cat with ears, a round body, and a tail, positioned as if pedaling a bicycle.\n", "- The bicycle is depicted with black lines, two white wheels, pedals, and a handlebar.\n", "- Attached to the bicycle is a black line connecting to a small wagon, where a smaller gray cat with ears and a smiling face is seated.\n", "\n", "### Background and Setting\n", "- The background features a solid light blue sky, suggesting a clear day.\n", "- A thin green strip at the bottom represents grass or ground, indicating an outdoor, open field or park-like setting.\n", "- There are small black dots near the rear wheel, possibly implying motion or dust.\n", "\n", "### Style and Tone\n", "- The style is minimalist and childlike, using basic geometric shapes, bold lines, and flat colors without shading or detail.\n", "- It resembles a simple digital drawing or doodle, with no intricate textures or realism.\n", "- The tone is cheerful and whimsical, conveyed by the smiling faces of both cats and the playful activity, evoking a sense of fun and adventure."], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["rsp = create_and_describe_image(\n", "    \"Create an SVG of a two cats riding a bicycle\",\n", "    client=wrapped_grok_client,\n", "    model_name=\"grok-4-0709\",\n", "    generation_kwargs={\"max_tokens\": 10000},\n", ")\n", "\n", "display(Image(filename=\"_temp/created_image.png\"))\n", "display(Markdown(rsp[\"description\"]))\n"]}, {"cell_type": "markdown", "id": "5c1fc1ed", "metadata": {}, "source": ["## Scorers\n"]}, {"cell_type": "code", "execution_count": null, "id": "5893b711", "metadata": {}, "outputs": [], "source": ["from pydantic import BaseModel, Field\n", "\n", "\n", "class LikertScale(BaseModel):\n", "    score: int = Field(\n", "        ...,\n", "        description=\"A score between 1 and 5 (1 is the worst score and 5 is the best score).\",\n", "        min_value=1,\n", "        max_value=5,\n", "    )  # type: ignore\n", "    rationale: str = Field(..., description=\"A rationale for the score.\")\n"]}, {"cell_type": "code", "execution_count": null, "id": "2822c402", "metadata": {}, "outputs": [], "source": ["def ask_llm_judge_about_image_description(client, model_name, input, output):\n", "    gen_kwargs = {\"response_format\": LikertScale}\n", "    if model_name.startswith(\"claude\"):\n", "        gen_kwargs = {}\n", "\n", "    rsp = client.chat.completions.parse(\n", "        model=model_name,\n", "        messages=[\n", "            {\n", "                \"role\": \"system\",\n", "                \"content\": dedent(\"\"\"\\\n", "                    You are a critical expert in determining if a generated image matches what the user asked for and whether or not an AI model did a good job in describing that image.\n", "                    \n", "                    The score must be an integer between 1 and 5.  You should respond ONLY with a JSON object with this format:{score:int, rationale:str}. Make sure you escape any characters that are not valid JSON. \n", "                    Only response with a string that can be parsed as JSON using `json.loads()`. Double check your work!\n", "                    \"\"\"),\n", "            },\n", "            {\n", "                \"role\": \"user\",\n", "                \"content\": [\n", "                    {\"type\": \"text\", \"text\": f\"Here is the image generated from the description: {input}\"},\n", "                    {\"type\": \"image_url\", \"image_url\": {\"url\": output[\"image_url\"]}},\n", "                    {\n", "                        \"type\": \"text\",\n", "                        \"text\": f\"Here is the description of the generated image: {output['description']}\",\n", "                    },\n", "                    {\n", "                        \"type\": \"text\",\n", "                        \"text\": \"Return a score between 1 and 5 based on how well the image matches the description and how well the description matches the image. 1 is the worst score and 5 is the best score.\",\n", "                    },\n", "                ],\n", "            },\n", "        ],\n", "        **gen_kwargs,\n", "    )\n", "\n", "    if model_name.startswith(\"claude\"):\n", "        parsed = json.loads(rsp.choices[0].message.content)\n", "        return (parsed[\"score\"] - 1) / 4\n", "    else:\n", "        parsed: LikertScale = rsp.choices[0].message.parsed\n", "        return (parsed.score - 1) / 4"]}, {"cell_type": "code", "execution_count": null, "id": "d1694b1e", "metadata": {}, "outputs": [], "source": ["def is_good_description(input, output, expected=None, metadata=None):\n", "    oai_judge_score = partial(\n", "        ask_llm_judge_about_image_description, client=openai_client, model_name=\"gpt-4o\", input=input, output=output\n", "    )()\n", "    anthropic_judge_score = partial(\n", "        ask_llm_judge_about_image_description,\n", "        client=anthropic_client,\n", "        model_name=\"claude-3-5-sonnet-20240620\",\n", "        input=input,\n", "        output=output,\n", "    )()\n", "    grok_judge_score = partial(\n", "        ask_llm_judge_about_image_description,\n", "        client=wrapped_grok_client,\n", "        model_name=\"grok-4-0709\",\n", "        input=input,\n", "        output=output,\n", "    )()\n", "\n", "    return [\n", "        Score(name=\"is_good_description_judge_oai\", score=oai_judge_score),\n", "        Score(name=\"is_good_description_judge_anthropic\", score=anthropic_judge_score),\n", "        Score(name=\"is_good_description_judge_grok\", score=grok_judge_score),\n", "        Score(name=\"is_good_description_jury\", score=(oai_judge_score + anthropic_judge_score + grok_judge_score) / 3),\n", "    ]"]}, {"cell_type": "code", "execution_count": 20, "id": "e45374ea", "metadata": {}, "outputs": [{"data": {"text/plain": ["[Score(name='is_good_description_judge_oai', score=1.0, metadata={}, error=None),\n", " Score(name='is_good_description_judge_anthropic', score=0.5, metadata={}, error=None),\n", " Score(name='is_good_description_judge_grok', score=0.5, metadata={}, error=None),\n", " Score(name='is_good_description_jury', score=0.6666666666666666, metadata={}, error=None)]"]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["score = is_good_description(\n", "    input=\"Create an SVG of a two cats riding a bicycle\",\n", "    output=rsp,\n", ")\n", "\n", "score"]}, {"cell_type": "markdown", "id": "7245ec3c", "metadata": {}, "source": ["## Evals\n"]}, {"cell_type": "code", "execution_count": 21, "id": "ed2dea7d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025071312\n"]}], "source": ["current_date_str = datetime.now().strftime(\"%Y%m%d%H\")\n", "print(current_date_str)\n"]}, {"cell_type": "code", "execution_count": null, "id": "13e24143", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Skipping git metadata. This is likely because the repository has not been published to a remote yet. Remote named 'origin' didn't exist\n", "Experiment reasoning-xai-grok4-0709-2025071312 is running at https://www.braintrust.dev/app/braintrustdata.com/p/wayde-bt-new-model-benchmark/experiments/reasoning-xai-grok4-0709-2025071312\n", "wayde-bt-new-model-benchmark [experiment_name=reasoning-xai-grok4-0709-2025071312] (data): 2it [00:00, 7752.87it/s]\n", "wayde-bt-new-model-benchmark [experiment_name=reasoning-xai-grok4-0709-2025071312] (tasks): 100%|██████████| 2/2 [01:19<00:00, 39.98s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=========================SUMMARY=========================\n", "50.00% 'is_good_description_judge_anthropic' score\n", "37.50% 'is_good_description_judge_grok'      score\n", "62.50% 'is_good_description_judge_oai'       score\n", "50.00% 'is_good_description_jury'            score\n", "\n", "1752435968.86s start\n", "1752436042.77s end\n", "35.16s duration\n", "35.10s llm_duration\n", "326tok prompt_tokens\n", "1472.50tok completion_tokens\n", "1798.50tok total_tokens\n", "0.02$ estimated_cost\n", "4tok prompt_cached_tokens\n", "0tok prompt_cache_creation_tokens\n", "\n", "See results for reasoning-xai-grok4-0709-2025071312 at https://www.braintrust.dev/app/braintrustdata.com/p/wayde-bt-new-model-benchmark/experiments/reasoning-xai-grok4-0709-2025071312\n"]}, {"data": {"text/plain": ["EvalResultWithSummary(summary=\"...\", results=[...])"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["await bt.<PERSON><PERSON><PERSON>(\n", "    name=BT_PROJECT_NAME,\n", "    experiment_name=f\"reasoning-xai-grok4-0709-{current_date_str}\",\n", "    data=lambda: [\n", "        bt.Eval<PERSON>ase(input=\"Generate an SVG of a pelican riding a bicycle\"),\n", "        bt.EvalCase(input=\"Generate an SVG of two cats driving a tesla\"),\n", "    ],  # type: ignore\n", "    task=partial(\n", "        create_and_describe_image,\n", "        client=wrapped_grok_client,\n", "        model_name=\"grok-4-0709\",\n", "        generation_kwargs={\"max_tokens\": 10000},\n", "    ),\n", "    scores=[is_good_description],\n", "    metadata={\"vendor\": \"xai\", \"model\": \"grok-4-0709\"},\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "d5d204f3", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "afc1d1b8", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.11"}}, "nbformat": 4, "nbformat_minor": 5}