import { z } from "zod";
import { useEntityStorage } from "#/lib/clientDataStorage";

import * as semver from "semver";
import { useAPIVersion } from "#/ui/api-version/check-api-version";
import { useCallback, useMemo } from "react";
import { MINIMUM_BTQL_API_VERSION } from "#/utils/btql/constants";
import { useIsClient } from "#/utils/use-is-client";
import { isEmpty } from "#/utils/object";
import { useOrg } from "#/utils/user";
import { useHypertune } from "#/generated/hypertune.react";
import { type FlagOptions } from "#/generated/hypertune";

export const featureFlagsSchema = z
  .strictObject({
    enableAdvancedMetrics: z.boolean().optional().default(true),
    columnstoreSearch: z.boolean().optional().default(false),
    disableColumnstoreSearch: z.boolean().optional().default(false),
    disableRealtime: z.boolean().optional().default(false),
    functions: z.boolean().optional().default(true),
    views: z.boolean().optional().default(true),
    errorCount: z.boolean().optional().default(true),
    btqlMatchFilters: z.boolean().optional().default(false),
    btqlFastSearch: z.boolean().optional().default(false),
    customFunctions: z.boolean().optional().default(true),
    functionOrigin: z.boolean().optional().default(true),
    copilot: z.boolean().optional().default(true),
    copilotData: z.boolean().optional().default(true),
    copilotPrompts: z.boolean().optional().default(true),
    functionTools: z.boolean().optional().default(true),
    hasIsRootField: z.boolean().optional().default(true),
    customColumns: z.boolean().optional().default(true),
    brainstore: z.boolean().optional().default(false),
    brainstore_realtime: z.boolean().optional().default(true),
    brainstore_skip_backfill_check: z.boolean().optional().default(false),
    advancedSearch: z.boolean().optional().default(false),
    playX: z.boolean().optional().default(true),
    playXStop: z.boolean().optional().default(true),
    disableObjectCache: z.boolean().optional().default(isFirefox()),
    enableExpensiveSummaries: z.boolean().optional().default(false),
    queryDiagnostics: z.boolean().optional().default(false),
    fastExperimentSummary: z.boolean().optional().default(false),
    fastDatasetSummary: z.boolean().optional().default(false),
    schemaInference: z.boolean().optional().default(true),
    playxExtraMessages: z.boolean().optional().default(true),
    agents: z.boolean().optional().default(true),
    attachmentsInMessages: z.boolean().optional().default(true),
    remoteEvals: z.boolean().optional().default(true),
    flattenedBoolOps: z.boolean().optional().default(true),
    manyExperiments: z.boolean().optional().default(false),
    automations: z.boolean().optional().default(true),
    automationsExport: z.boolean().optional().default(true),
    thinking: z.boolean().optional().default(true),
    loop: z.boolean().optional().default(true),
    datasetAgentVar: z.boolean().optional().default(true),
    projectSummaryMetrics: z.boolean().optional().default(true),
    onlineScoringFilterAndTest: z.boolean().optional().default(true),
    summarySorting: z.boolean().optional().default(true),
    serviceAccounts: z.boolean().optional().default(false),
  })
  .strip();
export type FeatureFlags = z.infer<typeof featureFlagsSchema>;

export const featureFlagConfig: Record<
  keyof FeatureFlags,
  {
    title: string;
    description?: string;
    minVersion: string;
    minDefaultVersion?: string;
    isHidden?: boolean;
    dependsOn?: keyof FeatureFlags;
    isDebugFlag?: boolean;
    onPremOnly?: boolean;
  }
> = {
  enableAdvancedMetrics: {
    title: "Additional summary metrics",
    description:
      "Display additional columns on the projects list table (eg. token count, ttf token, logs count, error count).",
    minVersion: MINIMUM_BTQL_API_VERSION,
    dependsOn: "brainstore",
    isDebugFlag: true,
  },
  enableExpensiveSummaries: {
    title: "Expensive summaries",
    description:
      "Enable expensive summary queries, even if you are running without a columnstore backend.",
    minVersion: MINIMUM_BTQL_API_VERSION,
    isDebugFlag: true,
    onPremOnly: true,
  },
  brainstore: {
    title: "Brainstore",
    minVersion: "0.0.62",
    description:
      "Next-gen data storage and query engine. Once enabled, Brainstore automatically powers search, analytics, and data loading in the UI.",
  },
  functions: {
    title: "Functions",
    minVersion: "0.0.50",
    isHidden: true,
  },
  views: {
    title: "Saved table views",
    minVersion: "0.0.49",
    isHidden: true,
  },
  errorCount: {
    title: "Error count",
    description: "Enable error count",
    minVersion: "0.0.52",
    isHidden: true,
  },
  customFunctions: {
    title: "Custom functions",
    minVersion: "0.0.53",
    isHidden: true,
  },
  functionOrigin: {
    title: "Function origin",
    minVersion: "0.0.54",
    isHidden: true,
  },
  copilot: {
    title: "Copilot",
    minVersion: "0.0.0",
    isHidden: true,
  },
  copilotData: {
    title: "Copilot data",
    minVersion: "0.0.0",
    isHidden: true,
  },
  copilotPrompts: {
    title: "Copilot prompts",
    minVersion: "0.0.0",
    isHidden: true,
  },
  functionTools: {
    title: "Function tools",
    minVersion: "0.0.56",
    isHidden: true,
  },
  hasIsRootField: {
    title: "Has the is_root field",
    minVersion: "0.0.57",
    isHidden: true,
  },
  customColumns: {
    title: "Custom table columns",
    minVersion: "0.0.60",
    isHidden: true,
  },
  btqlMatchFilters: {
    title: "Full-text-search index for BTQL match filters",
    description:
      "Enables an experimental full-text-search index for BTQL match filters. This should only be enabled when working with Braintrust support.",
    minVersion: "0.0.52",
    isDebugFlag: true,
  },
  btqlFastSearch: {
    title: "BTQL fast search",
    description: "Enables an experimental fast search mode for BTQL.",
    minVersion: "0.0.58",
    isDebugFlag: true,
  },
  columnstoreSearch: {
    title: "Force columnstore for log search",
    description:
      "Unlikely to improve performance, but can be useful when working with Braintrust support to test certain optimizations.",
    minVersion: MINIMUM_BTQL_API_VERSION,
    isDebugFlag: true,
  },
  disableColumnstoreSearch: {
    title: "Never use columnstore for log search",
    description:
      "By default, columnstore is dynamically enabled for log search. This flag disables columnstore for log search.",
    minVersion: MINIMUM_BTQL_API_VERSION,
    isDebugFlag: true,
  },
  disableRealtime: {
    title: "Disable real-time updates",
    description:
      "Disable real-time updates throughout the application. This should only be enabled for debugging purposes.",
    minVersion: "0.0.0",
    isDebugFlag: true,
  },
  brainstore_realtime: {
    title: "Brainstore realtime",
    minVersion: "0.0.60",
    description:
      "Enable realtime updates for Brainstore. You should only turn this off if debugging a performance issue with the Braintrust team.",
    dependsOn: "brainstore",
    isDebugFlag: true,
    isHidden: true,
  },
  brainstore_skip_backfill_check: {
    title: "Brainstore skip backfill check",
    minVersion: "0.0.60",
    description:
      "Skip checking if the data has been backfilled before using Brainstore. You should only turn this on if debugging a performance issue with the Braintrust team.",
    dependsOn: "brainstore",
    isDebugFlag: true,
    isHidden: true,
  },
  queryDiagnostics: {
    title: "Query diagnostics",
    minVersion: "0.0.60",
    description:
      "Include additional query diagnostics to help understand query performance.",
    dependsOn: "brainstore",
    isDebugFlag: true,
  },
  advancedSearch: {
    title: "Advanced search",
    minVersion: "0.0.60",
    isHidden: true,
  },
  playX: {
    title: "Full evals in playground",
    description:
      "Run full evaluations in the playground, with robust filtering, visualization and large dataset support.",
    minVersion: "0.0.63",
    isHidden: true,
  },
  playXStop: {
    title: "Stop playground evaluations server-side",
    description:
      "Aborts the evaluation server-side to prevent unnecessary model calls.",
    minVersion: "0.0.64",
    dependsOn: "playX",
    isHidden: true,
  },
  playxExtraMessages: {
    title: "Extra messages in playground",
    description:
      "Enable extra messages in the playground. This should only be enabled for debugging purposes.",
    minVersion: "0.0.65",
    isHidden: true,
  },
  fastExperimentSummary: {
    title: "Faster tables",
    description:
      "Load summary tables faster using some new optimizations. Currently will speed up the logs, experiments, and datasets views. Playgrounds soon!",
    minVersion: "0.0.65",
    minDefaultVersion: "0.0.73",
    isDebugFlag: true,
    dependsOn: "brainstore",
  },
  fastDatasetSummary: {
    title: "Faster tables for datasets",
    description:
      "Load dataset tables faster using fastExperimentSummary optimizations.",
    minVersion: "0.0.73",
    minDefaultVersion: "0.0.73",
    isDebugFlag: true,
    dependsOn: "brainstore",
    isHidden: true,
  },
  schemaInference: {
    title: "Schema inference",
    description: "Enable schema inference queries.",
    minVersion: "0.0.66",
    isHidden: true,
    dependsOn: "brainstore",
  },
  disableObjectCache: {
    title: "Disable object cache",
    description:
      "Disable the object cache (used to save experiments and playgrounds in your browser). This should only be enabled for debugging purposes.",
    minVersion: "0.0.0",
    isDebugFlag: true,
  },
  agents: {
    title: "Agents",
    description: "Prompt chaining in playgrounds.",
    minVersion: "0.0.66",
    dependsOn: "playX",
    isHidden: true,
  },
  attachmentsInMessages: {
    title: "Attachments in messages",
    minVersion: "0.0.68",
    isHidden: true,
  },
  remoteEvals: {
    title: "Run evals on remote servers",
    description: "Enable remote evals in the playground.",
    minVersion: "0.0.66",
    isHidden: true,
  },
  flattenedBoolOps: {
    title: "Flattened boolean ops",
    description: "Enable flattened boolean ops (makes BTQL parser faster).",
    minVersion: "0.0.69",
    isDebugFlag: true,
    isHidden: true,
  },
  manyExperiments: {
    title: "Fetch 2000 experiments (instead of 500) on the experiments page.",
    minVersion: MINIMUM_BTQL_API_VERSION,
    isDebugFlag: true,
  },
  automations: {
    title: "Automations",
    minVersion: "0.0.72",
    minDefaultVersion: "1.1.0",
    isHidden: true,
    dependsOn: "brainstore_realtime",
  },
  automationsExport: {
    title: "Automations export",
    minVersion: "0.0.75",
    minDefaultVersion: "1.1.0",
    isHidden: true,
    dependsOn: "automations",
  },
  thinking: {
    title: "Support AI Models with Reasoning Params & Thinking Tokens",
    minVersion: "0.0.74",
    isHidden: true,
  },
  loop: {
    title: "Loop",
    description: "AI optimize and generate prompts and data in playgrounds",
    minVersion: "0.0.74",
    isHidden: true,
  },
  datasetAgentVar: {
    title: "Dataset variable in agents",
    minVersion: "1.1.1",
    minDefaultVersion: "1.1.1",
    isHidden: true,
    dependsOn: "agents",
  },
  projectSummaryMetrics: {
    title: "Project summary metrics",
    description: "Display metrics in the project page.",
    minVersion: MINIMUM_BTQL_API_VERSION,
    isDebugFlag: true,
  },
  onlineScoringFilterAndTest: {
    title: "Online scoring filter and test",
    minVersion: "1.1.10",
    minDefaultVersion: "1.1.10",
    isHidden: true,
    dependsOn: "automations",
  },
  summarySorting: {
    title: "Sorting with summary shape",
    description: "Enable sorting in tables using summary shape.",
    minVersion: "1.1.12",
    isHidden: true,
    dependsOn: "brainstore",
  },
  serviceAccounts: {
    title: "Service accounts",
    isHidden: true,
    minVersion: "1.1.12",
    dependsOn: "automations",
  },
};

// eslint-disable-next-line @typescript-eslint/consistent-type-assertions
export const MinVersion = Object.fromEntries(
  Object.entries(featureFlagConfig).map(([key, def]) => [key, def.minVersion]),
) as Record<keyof FeatureFlags, string>;

export function useFeatureFlags(): {
  flags: FeatureFlags;
  forcedFlags: Record<string, boolean>;
  setFlag: (flag: keyof FeatureFlags, enabled: boolean) => void;
  isLoading: boolean;
} {
  const isClient = useIsClient();
  const [value, setValue] = useEntityStorage({
    entityType: "app",
    entityIdentifier: "",
    key: "featureFlags",
  });

  const hypertune = useHypertune();

  const setFlag = useCallback(
    (flag: keyof FeatureFlags, enabled: boolean): void => {
      setValue((flags) => ({ ...flags, [flag]: enabled }));
    },
    [setValue],
  );

  const { version: apiVersion, brainstore_default } = useAPIVersion();
  const org = useOrg();

  const { effectiveFlags, forcedFlags } = useMemo(() => {
    const hypertuneRoot = hypertune.get();
    const forced: Record<string, boolean> = {};
    // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
    const flags = Object.fromEntries(
      Object.entries(featureFlagConfig).map(([key, def]) => {
        const shouldCheckMinVersion = def.minVersion !== "0.0.0";
        if (
          shouldCheckMinVersion &&
          (!apiVersion || semver.lt(apiVersion, def.minVersion))
        ) {
          return [key, false];
        } else {
          // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
          const k = key as keyof FeatureFlags;
          const userSetting = value?.[k];

          // getFieldValue will throw error if missing from schema
          // so first check it exists in root
          let remoteValue: FlagOptions = "configurable";
          if (k in hypertuneRoot) {
            try {
              const flagGet = hypertune.getFieldValue(k, {
                fallback: "configurable",
              });
              if (flagGet === "forceOn" || flagGet === "forceOff") {
                remoteValue = flagGet;
              }
            } catch {
              // ignore get failure
            }
          }

          if (remoteValue === "forceOn") {
            forced[key] = true;
            return [key, true];
          } else if (remoteValue === "forceOff") {
            forced[key] = true;
            return [key, false];
          } else if (!isEmpty(userSetting)) {
            // User override when no remote value
            const parsedValue = featureFlagsSchema.shape[k]?.parse(userSetting);
            return [key, parsedValue];
          } else {
            // Fall back to existing logic for code defaults
            if (
              k === "brainstore" &&
              brainstore_default &&
              isEmpty(userSetting)
            ) {
              return [key, true];
            } else if (
              isEmpty(userSetting) &&
              ((k === "enableAdvancedMetrics" &&
                ORGS_WITHOUT_ADVANCED_METRICS.includes(org.name)) ||
                (k === "projectSummaryMetrics" &&
                  ORGS_WITHOUT_PROJECT_SUMMARY.includes(org.name)))
            ) {
              return [key, false];
            } else if (
              def.minDefaultVersion &&
              semver.gte(apiVersion, def.minDefaultVersion) &&
              isEmpty(userSetting) &&
              brainstore_default
            ) {
              return [key, true];
            }
            const parsedValue = featureFlagsSchema.shape[k]?.parse(undefined);
            return [key, parsedValue];
          }
        }
      }),
    ) as FeatureFlags;
    const checked = new Set<keyof FeatureFlags>();
    for (const flag of Object.keys(flags)) {
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions
      disableDependentFlag(flags, flag as keyof FeatureFlags, checked);
    }
    return { effectiveFlags: flags, forcedFlags: forced };
  }, [apiVersion, value, brainstore_default, org.name, hypertune]);

  const effectiveFlagsHash = useMemo(() => {
    return JSON.stringify(effectiveFlags);
  }, [effectiveFlags]);

  const forcedFlagsHash = useMemo(() => {
    return JSON.stringify(forcedFlags);
  }, [forcedFlags]);

  return useMemo(() => {
    return {
      flags: effectiveFlags,
      forcedFlags,
      setFlag,
      isLoading: !isClient,
    };
    // only update when one of the deep values of flags or forced changes instead of object
    // eslint-disable-next-line react-compiler/react-compiler
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [effectiveFlagsHash, forcedFlagsHash, setFlag, isClient]);
}

/** Memo'd featured flag check */
export const useIsFeatureEnabled = (flagKey: keyof FeatureFlags) => {
  const { flags } = useFeatureFlags();
  const isEnabled = flags[flagKey];

  return useMemo(() => {
    return isEnabled;
  }, [isEnabled]);
};

function isFirefox(): boolean {
  if (typeof window === "undefined") return false;
  return navigator.userAgent.toLowerCase().includes("firefox");
}

export const FAST_OBJECT_TYPES = ["experiment", "project_logs"];

function disableDependentFlag(
  flags: FeatureFlags,
  flag: keyof FeatureFlags,
  checked: Set<keyof FeatureFlags>,
) {
  const config = featureFlagConfig[flag];
  if (!config || checked.has(flag)) {
    return;
  }
  checked.add(flag);
  if (config.dependsOn) {
    disableDependentFlag(flags, config.dependsOn, checked);
    flags[flag] = flags[flag] && flags[config.dependsOn];
  }
  return;
}

export const ORGS_WITHOUT_PROJECT_SUMMARY = ["Portola AI", "Perplexity AI"];
export const ORGS_WITHOUT_ADVANCED_METRICS = [
  "Netflix",
  "zapier.com",
  ...ORGS_WITHOUT_PROJECT_SUMMARY,
];
