import { type FormatterProps } from "#/ui/arrow-table";
import { DiffRightField, isDiffObject } from "#/utils/diffs/diff-objects";
import { DefaultValueDisplay } from "./default-formatter";

export function InputFormatter<TsData, TsValue>(
  props: FormatterProps<TsData, TsValue>,
) {
  const { value, meta } = props;
  if (!value?.toJSON) {
    return (
      <DefaultValueDisplay
        {...props}
        value={value}
        metaType={meta.type}
        typeHint={meta.typeHint}
        moreText={meta.moreText}
        isGridLayout={meta.isGridLayout}
      />
    );
  }

  const valueJson = value.toJSON();
  const metaTypeFirstChild = meta.type.children[0];
  if (!isDiffObject(valueJson) || !metaTypeFirstChild) {
    return null;
  }

  return (
    <DefaultValueDisplay
      {...props}
      value={valueJson[DiffRightField]}
      metaType={metaTypeFirstChild.type}
      typeHint={meta.typeHint}
      moreText={meta.moreText}
      isGridLayout={meta.isGridLayout}
    />
  );
}
