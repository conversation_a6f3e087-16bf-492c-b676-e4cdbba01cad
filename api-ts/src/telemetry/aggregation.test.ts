import { test, expect, beforeAll, afterAll, beforeEach } from "vitest";
import {
  aggregate,
  clearEnvTelemetryOverride,
  setEnvTelemetryOverride,
} from "./aggregation";
import { AnyEvent } from "./types";

let original: string | undefined;

beforeAll(() => {
  original = process.env.TELEMETRY_ENABLE_AGGREGATION;
});

beforeEach(async () => {
  process.env.TELEMETRY_ENABLE_AGGREGATION = "true";
  await clearEnvTelemetryOverride();
});

afterAll(() => {
  process.env.TELEMETRY_ENABLE_AGGREGATION = original;
});

test("aggregates FunctionInvokedEvent by external_customer_id", async () => {
  const events: AnyEvent[] = [
    {
      event_name: "FunctionInvokedEvent",
      timestamp: new Date().toISOString(),
      external_customer_id: "customer1",
      idempotency_key: "key1",
      properties: { org_id: "org1" },
    },
    {
      event_name: "FunctionInvokedEvent",
      timestamp: new Date().toISOString(),
      external_customer_id: "customer2",
      idempotency_key: "key2",
      properties: { org_id: "org2" },
    },
    {
      event_name: "FunctionInvokedEvent",
      timestamp: new Date().toISOString(),
      external_customer_id: "customer1",
      idempotency_key: "key3",
      properties: { org_id: "org1" },
    },
  ];

  const result = await aggregate(events);

  expect(result.original).toEqual([]);
  expect(Object.keys(result.aggregated)).toHaveLength(2);

  const aggregatedCustomer1 =
    result.aggregated["FunctionInvokedEvent:customer1"];
  expect(aggregatedCustomer1).toBeDefined();
  expect(aggregatedCustomer1.event_name).toBe("FunctionInvokedEvent");
  expect(aggregatedCustomer1.properties.count).toBe(2);

  const aggregatedCustomer2 =
    result.aggregated["FunctionInvokedEvent:customer2"];
  expect(aggregatedCustomer2).toBeDefined();
  expect(aggregatedCustomer2.event_name).toBe("FunctionInvokedEvent");
  expect(aggregatedCustomer2.properties.count).toBe(1);
});

test("aggregates LogInsertedEvent by external_customer_id", async () => {
  const events: AnyEvent[] = [
    {
      event_name: "LogInsertedEvent",
      timestamp: new Date().toISOString(),
      external_customer_id: "customerA",
      idempotency_key: "logkey1",
      properties: {
        count: 1,
        agg_log_bytes: 100,
        agg_metrics_count: 5,
        agg_scores_count: 2,
      },
    },
    {
      event_name: "LogInsertedEvent",
      timestamp: new Date().toISOString(),
      external_customer_id: "customerB",
      idempotency_key: "logkey2",
      properties: {
        count: 1,
        agg_log_bytes: 200,
        agg_metrics_count: 10,
        agg_scores_count: 3,
      },
    },
    {
      event_name: "LogInsertedEvent",
      timestamp: new Date().toISOString(),
      external_customer_id: "customerA",
      idempotency_key: "logkey3",
      properties: {
        count: 1,
        agg_log_bytes: 150,
        agg_metrics_count: 8,
        agg_scores_count: 1,
      },
    },
  ];

  const result = await aggregate(events);

  expect(result.original).toEqual([]);
  expect(Object.keys(result.aggregated)).toHaveLength(2);

  const aggregatedCustomerA = result.aggregated["LogInsertedEvent:customerA"];
  expect(aggregatedCustomerA).toBeDefined();
  expect(aggregatedCustomerA.event_name).toBe("LogInsertedEvent");
  expect(aggregatedCustomerA.properties.count).toBe(2);
  expect(aggregatedCustomerA.properties.agg_log_bytes).toBe(250); // 100 + 150
  expect(aggregatedCustomerA.properties.agg_metrics_count).toBe(13); // 5 + 8
  expect(aggregatedCustomerA.properties.agg_scores_count).toBe(3); // 2 + 1

  const aggregatedCustomerB = result.aggregated["LogInsertedEvent:customerB"];
  expect(aggregatedCustomerB).toBeDefined();
  expect(aggregatedCustomerB.event_name).toBe("LogInsertedEvent");
  expect(aggregatedCustomerB.properties.count).toBe(1);
  expect(aggregatedCustomerB.properties.agg_log_bytes).toBe(200);
  expect(aggregatedCustomerB.properties.agg_metrics_count).toBe(10);
  expect(aggregatedCustomerB.properties.agg_scores_count).toBe(3);
});

test("handles mixed event types and non-aggregatable events", async () => {
  const originalEvent = {
    event_name: "SomeOtherEvent",
    timestamp: new Date().toISOString(),
    external_customer_id: "customerX",
    idempotency_key: "key1",
    properties: {},
  };

  const events: AnyEvent[] = [
    {
      event_name: "FunctionInvokedEvent",
      timestamp: new Date().toISOString(),
      external_customer_id: "customer1",
      idempotency_key: "key1",
      properties: { org_id: "org1" },
    },
    {
      event_name: "LogInsertedEvent",
      timestamp: new Date().toISOString(),
      external_customer_id: "customer1",
      idempotency_key: "logkey1",
      properties: {
        agg_log_bytes: 10,
        agg_metrics_count: 1,
        agg_scores_count: 1,
      },
    },
    originalEvent,
    {
      event_name: "FunctionInvokedEvent",
      timestamp: new Date().toISOString(),
      external_customer_id: "customer1",
      idempotency_key: "key2",
      properties: { org_id: "org1" },
    },
  ];

  const result = await aggregate(events);

  expect(result.original).toEqual([originalEvent]);
  expect(Object.keys(result.aggregated)).toHaveLength(2); // One aggregated FunctionInvoked, one aggregated LogInserted

  const aggregatedFunctionInvoked =
    result.aggregated["FunctionInvokedEvent:customer1"];
  expect(aggregatedFunctionInvoked).toBeDefined();
  expect(aggregatedFunctionInvoked.properties.count).toBe(2);

  const aggregatedLogInserted = result.aggregated["LogInsertedEvent:customer1"];
  expect(aggregatedLogInserted).toBeDefined();
  expect(aggregatedLogInserted.properties.count).toBe(1);
  expect(aggregatedLogInserted.properties.agg_log_bytes).toBe(10);
});

test("handles empty event list", async () => {
  const events: AnyEvent[] = [];
  const result = await aggregate(events);
  expect(result.original).toEqual([]);
  expect(result.aggregated).toEqual({});
});

test("skips aggregation if disabled", async () => {
  process.env.TELEMETRY_ENABLE_AGGREGATION = "";

  const events: AnyEvent[] = [
    {
      event_name: "LogInsertedEvent",
      timestamp: new Date().toISOString(),
      external_customer_id: "customerA",
      idempotency_key: "logkey1",
      properties: {
        count: 1,
        agg_log_bytes: 100,
        agg_metrics_count: 5,
        agg_scores_count: 2,
      },
    },
    {
      event_name: "LogInsertedEvent",
      timestamp: new Date().toISOString(),
      external_customer_id: "customerB",
      idempotency_key: "logkey2",
      properties: {
        count: 1,
        agg_log_bytes: 200,
        agg_metrics_count: 10,
        agg_scores_count: 3,
      },
    },
    {
      event_name: "LogInsertedEvent",
      timestamp: new Date().toISOString(),
      external_customer_id: "customerA",
      idempotency_key: "logkey3",
      properties: {
        count: 1,
        agg_log_bytes: 150,
        agg_metrics_count: 8,
        agg_scores_count: 1,
      },
    },
  ];

  const result = await aggregate(events);

  expect(result.original).toEqual(events);
  expect(Object.keys(result.aggregated)).toHaveLength(0);
});

test("skips aggregation if disabled with override", async () => {
  process.env.TELEMETRY_ENABLE_AGGREGATION = "true";

  await setEnvTelemetryOverride("customerB", "");

  const events: AnyEvent[] = [
    {
      event_name: "LogInsertedEvent",
      timestamp: new Date().toISOString(),
      external_customer_id: "customerA",
      idempotency_key: "logkey1",
      properties: {
        count: 1,
        agg_log_bytes: 100,
        agg_metrics_count: 5,
        agg_scores_count: 2,
      },
    },
    {
      event_name: "LogInsertedEvent",
      timestamp: new Date().toISOString(),
      external_customer_id: "customerB",
      idempotency_key: "logkey2",
      properties: {
        count: 1,
        agg_log_bytes: 200,
        agg_metrics_count: 10,
        agg_scores_count: 3,
      },
    },
    {
      event_name: "LogInsertedEvent",
      timestamp: new Date().toISOString(),
      external_customer_id: "customerA",
      idempotency_key: "logkey3",
      properties: {
        count: 1,
        agg_log_bytes: 150,
        agg_metrics_count: 8,
        agg_scores_count: 1,
      },
    },
  ];

  const { aggregated, original } = await aggregate(events);

  expect(original).toEqual([events[1]]);
  expect(aggregated).toEqual({
    "LogInsertedEvent:customerA": {
      event_name: "LogInsertedEvent",
      external_customer_id: "customerA",
      idempotency_key: "logkey1",
      properties: {
        count: 2,
        log_bytes: 0,
        metrics_count: 0,
        scores_count: 0,
        agg_log_bytes: 250,
        agg_metrics_count: 13,
        agg_scores_count: 3,
      },
      timestamp: expect.any(String),
    },
  });
});
