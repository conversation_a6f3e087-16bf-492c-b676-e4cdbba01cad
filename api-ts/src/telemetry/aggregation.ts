import { Organization } from "@braintrust/core/typespecs";
import { makeError } from "@braintrust/local";
import {
  decryptMessage,
  encryptedMessageSchema,
  encryptMessage,
} from "@braintrust/proxy/utils";
import { z } from "zod";
import { parseBooleanEnv, SERVICE_TOKEN_SECRET_KEY } from "../env";
import {
  getCurrentSpan,
  logCounter,
  otelWrapTraced,
} from "../instrumentation/api";
import { useRedisNamespace } from "./cache";
import { type FunctionInvokedEvent, type LogInsertedEvent } from "./events";
import { logger } from "./logger";
import { Aggregated, AnyEvent } from "./types";
import { sha256 } from "../hash";
import { SLEEP_INTERVAL_SECONDS } from "./cron/constants";

const SLEEP_INTERVAL_MINUTES = Math.ceil(SLEEP_INTERVAL_SECONDS / 60 / 1000);
const WINDOW_SIZE_MINUTES = Math.ceil(SLEEP_INTERVAL_MINUTES / 2); // 3 minutes typically
const WINDOW_TTL_MINUTES = WINDOW_SIZE_MINUTES * 10; // 30 minutes typically

const CACHE_VERSION = 1;
const TELEMETRY_AGGREGATION_NAMESPACE = `telemetry:v${CACHE_VERSION}:aggregation`;

const getCurrentWindow = (): number => {
  return Math.floor(new Date().getTime() / (1000 * 60 * WINDOW_SIZE_MINUTES));
};

const windowSlotToDate = (windowSlot: number): Date => {
  return new Date(windowSlot * WINDOW_SIZE_MINUTES * 60 * 1000);
};

/**
 * Very simple batch function that splits an array into batches of a given size and executes them concurrently.
 * We do this to respect the Node event loop and yield control back to the event loop between batches.
 *
 * User beware. Promises must deal with their returns and exception handling.
 */
const batch = otelWrapTraced(
  "batch",
  async (
    name: string,
    promises: Promise<void>[],
    concurrency = 100,
  ): Promise<void> => {
    let count = 0;
    let errors = 0;
    for (let i = 0; i < promises.length; i += concurrency) {
      const batch = promises.slice(i, i + concurrency);
      count++;

      await Promise.all(batch).catch((e) => {
        errors++;
        // can't really do much here
        logger.error("Unexpectedly caught a batch raised error", {
          error: makeError(e),
        });
      });

      // Yield control back to event loop between batches
      await new Promise((resolve) => setImmediate(resolve));
    }

    const span = getCurrentSpan();
    span?.updateName(`batch: ${name}`);
    span?.setAttributes({
      errors,
      count,
      size: promises.length,
    });
  },
);

const getEnvTelemetryOverride = async (orgId: string) => {
  try {
    const { getKey, getClient } = useRedisNamespace(
      TELEMETRY_AGGREGATION_NAMESPACE,
    );
    const redis = await getClient();
    return (await redis.hGet(getKey("env"), orgId)) ?? undefined;
  } catch {}
};

export const setEnvTelemetryOverride = async (orgId: string, value: string) => {
  const { getKey, getClient } = useRedisNamespace(
    TELEMETRY_AGGREGATION_NAMESPACE,
  );
  const redis = await getClient();

  await redis.hSet(getKey("env"), orgId, value);
  await redis.expire(getKey("env"), 5 * 60); // 5 minutes
};

export const clearEnvTelemetryOverride = async (orgId?: string) => {
  const { getKey, getClient } = useRedisNamespace(
    TELEMETRY_AGGREGATION_NAMESPACE,
  );
  const redis = await getClient();

  if (orgId) {
    await redis.hDel(getKey("env"), orgId);
  } else {
    await redis.del(getKey("env"));
  }
};

const canOrgEventAggregate = async (orgId: string) => {
  const telementryEnvOverride = await getEnvTelemetryOverride(orgId);

  const TELEMETRY_ENABLE_AGGREGATION =
    telementryEnvOverride !== undefined
      ? telementryEnvOverride
      : process.env.TELEMETRY_ENABLE_AGGREGATION || "";

  const parsed = parseBooleanEnv(TELEMETRY_ENABLE_AGGREGATION);

  return parsed;
};

/**
 * Attempts to aggregate the events into a smaller subset of events partitioned by unique sets of metrics.
 * If unable to, we'll return the original events that needs to be emitted immediately.
 */
export const aggregate = async (events: AnyEvent[]) => {
  const original = [];
  const aggregated: Aggregated = {};

  for (const event of events) {
    // @ts-ignore
    const aggregator: (aggregated: Aggregated, event: AnyEvent) => void =
      // eslint-disable-next-line @typescript-eslint/consistent-type-assertions -- MISSING JUSTIFICATION
      aggregators[event.event_name as keyof typeof aggregators];

    const canAggregate = await canOrgEventAggregate(event.external_customer_id);
    if (!aggregator || !canAggregate) {
      original.push(event);
      continue;
    }

    try {
      aggregator(aggregated, event);
    } catch (e) {
      logger.error(
        `Failed to aggregate event ${event.event_name}: ${makeError(e).message}`,
      );
      original.push(event);
    }
  }

  return {
    aggregated,
    original,
  };
};

const aggregators = {
  FunctionInvokedEvent: (
    aggregated: Aggregated,
    event: FunctionInvokedEvent,
  ) => {
    // NOTE: assumes we only need to accumulate by org_id!
    const partition = `FunctionInvokedEvent:${event.external_customer_id}`;

    // @ts-ignore
    const acc: FunctionInvokedEvent & { properties: { count: number } } =
      aggregated[partition] ||
      (aggregated[partition] = {
        event_name: "FunctionInvokedEvent",
        timestamp: event.timestamp,
        external_customer_id: event.external_customer_id,
        idempotency_key: event.idempotency_key,
        properties: {
          count: 0,
        },
      });

    acc.properties.count += 1;
  },
  LogInsertedEvent: (aggregated: Aggregated, event: LogInsertedEvent) => {
    // NOTE: assumes we only need to accumulate by org_id!
    const partition = `LogInsertedEvent:${event.external_customer_id}`;

    // @ts-ignore
    const acc: LogInsertedEvent & {
      properties: {
        count: number;
        agg_metrics_count: number;
        agg_scores_count: number;
        agg_log_bytes: number;
      };
    } =
      aggregated[partition] ||
      (aggregated[partition] = {
        event_name: "LogInsertedEvent",
        timestamp: event.timestamp,
        external_customer_id: event.external_customer_id,
        idempotency_key: event.idempotency_key,
        properties: {
          count: 0,
          log_bytes: 0,
          metrics_count: 0,
          scores_count: 0,
          agg_metrics_count: 0,
          agg_scores_count: 0,
          agg_log_bytes: 0,
        },
      });

    acc.properties.count += 1;
    // TEMP: while we audit pre-aggregated events.. eventually we'll just use without input_ prefix
    acc.properties.agg_log_bytes += event.properties.agg_log_bytes || 0;
    acc.properties.agg_metrics_count += event.properties.agg_metrics_count || 0;
    acc.properties.agg_scores_count += event.properties.agg_scores_count || 0;
  },
} as const;

/**
 * We are storing the metrics & last known good token per event with time windowing
 *
 * (dirtySegment=telemetry:aggregation:dirty:{timeWindow})
 * (eventSegment=telemetry:aggregation:{timeWindow}:{org}:{event_name} ...
 *    (field=token)
 *    (field=metrics:{metric})
 *
 * A set of dirty keys are kept for the flush to avoid scanning.
 * Time windowing ensures we can process completed windows while avoiding the current working window.
 */
export const updateStore = otelWrapTraced(
  "updateStore",
  async ({
    events,
    token,
  }: {
    events: AnyEvent[];
    token: string | undefined;
  }) => {
    if (!events?.length) {
      return;
    }

    const encrypted = token ? await encrypt(token) : undefined;
    await batch(
      "updateStoreEvents",
      events.map((event) => updateStoreEvent(event, encrypted)),
    );
  },
);

const updateStoreEvent = async (
  event: AnyEvent,
  encrypted: Awaited<ReturnType<typeof encrypt>> | undefined,
) => {
  try {
    const { getKey, pipeline } = useRedisNamespace(
      TELEMETRY_AGGREGATION_NAMESPACE,
    );

    const currentWindow = getCurrentWindow();

    await pipeline((client) => {
      const dirtySegment = getKey(`dirty:${currentWindow}`);
      const eventSegment = getKey(
        `${currentWindow}:${event.external_customer_id}:${event.event_name}`,
      );

      if (encrypted) {
        client.hSet(eventSegment, "token", JSON.stringify(encrypted));
      }

      for (const [metric, value] of Object.entries(event.properties)) {
        client.hIncrBy(eventSegment, `metric:${metric}`, value);
      }

      // make sure we track the keys that we need to flush
      client.sAdd(dirtySegment, eventSegment);

      // protect our memory if we fail to flush
      client.expire(dirtySegment, WINDOW_TTL_MINUTES * 60);
      client.expire(eventSegment, WINDOW_TTL_MINUTES * 60);
    });
  } catch (e) {
    logCounter({
      name: "telemetry.errors.updateStore",
      value: 1,
      attributes: {
        eventName: event.event_name,
        orgId: event.external_customer_id,
      },
    });

    // If we fail to do any updates worst case scenario is that we undercount metrics. While that sucks, at least users will not be charged.
    // For free users, this may be a confusing state, though. they may run into plan limits earlier, and the usage reporting will be less than the actual usage.
    logger.error("Failed to update the aggregated telemetry store", {
      error: makeError(e),
    });
  }
};

const getServiceTokenSecretKey = async () => {
  if (!SERVICE_TOKEN_SECRET_KEY) {
    throw new Error("SERVICE_TOKEN_SECRET_KEY is not set");
  }
  return sha256(SERVICE_TOKEN_SECRET_KEY, "base64");
};

const encrypt = async (token: string) =>
  encryptMessage(await getServiceTokenSecretKey(), token);

export const decrypt = async (encrypted: string) => {
  try {
    const encryptedServiceTokenParsed = encryptedMessageSchema.parse(
      JSON.parse(encrypted),
    );

    return await decryptMessage(
      await getServiceTokenSecretKey(),
      encryptedServiceTokenParsed.iv,
      encryptedServiceTokenParsed.data,
    );
  } catch (e) {
    logger.warn("Failed to decrypt service token", {
      error: makeError(e),
    });
  }
};

type Flush = Record<Organization["id"], { token: string; events: AnyEvent[] }>;

const parseDirtySegment = (dirtySegment: string) => {
  const [telemetry, version, aggregation, dirty, currentWindow] =
    dirtySegment.split(":");
  return { telemetry, version, aggregation, dirty, currentWindow };
};

const parseDirtyKey = (dirtyKey: string) => {
  const [telemetry, version, aggregation, window, orgId, eventName] =
    dirtyKey.split(":");
  return {
    telemetry,
    version,
    aggregation,
    window,
    orgId,
    eventName,
  };
};

type Stats = {
  totalInitialKeys: number;
  windowsWithData: number;
  interrupted: boolean;
  lastIndex: number;
  lastDirtyCount: number;
};

/**
 * Flushes the telemetry aggregation store with time-windowing protection. This is tightly coupled with `updateStore`.
 * We deserialize stored token and metrics by looking at the specific dirty keys in completed time windows only.
 * This prevents infinite processing by avoiding the current working window where new updates are still happening.
 * We'll do this in a loop. While it is possible that updateStore to makes new updates, we intentionally only process
 * completed time windows, ensuring the flush will eventually complete.
 * After deserializizing (and flushing), we'll need to send the events up to the gateway.
 */
export const flushStore = otelWrapTraced(
  "flushStore",
  async ({
    onlyOrgId = "",
    canContinue = () => true,
    includeCurrentWindow = false,
  }: {
    onlyOrgId?: string;
    dirtyEventsCount?: number;
    canContinue?: () => boolean;
    includeCurrentWindow?: boolean;
  } = {}): Promise<Flush> => {
    const { getKey } = useRedisNamespace(TELEMETRY_AGGREGATION_NAMESPACE);

    const flushed: Flush = {};
    const currentWindow = getCurrentWindow();

    const maxWindowsToCheck = Math.ceil(
      WINDOW_TTL_MINUTES / WINDOW_SIZE_MINUTES,
    );

    const completedWindows = Array.from(
      { length: maxWindowsToCheck },
      (_, i) => {
        const windowSlot = currentWindow - (maxWindowsToCheck - i);
        return getKey(`dirty:${windowSlot}`);
      },
    );

    if (includeCurrentWindow) {
      completedWindows.push(getKey(`dirty:${currentWindow}`));
    }

    const stats: Stats = {
      totalInitialKeys: 0,
      windowsWithData: 0,
      interrupted: false,
      lastIndex: 0,
      lastDirtyCount: 0,
    };

    await batch(
      "flushWindow",
      completedWindows.map((window, index) =>
        flushWindow(window, index, stats, onlyOrgId, flushed, canContinue),
      ),
    );

    if (stats.totalInitialKeys > 0) {
      logCounter({
        name: "telemetry.flush.initial_dirty_keys",
        value: stats.totalInitialKeys,
        attributes: {},
      });
    }

    if (stats.windowsWithData > 0) {
      logCounter({
        name: "telemetry.flush.windows_with_data",
        value: stats.windowsWithData,
        attributes: {},
      });
    }

    if (completedWindows.length) {
      if (stats.lastIndex === completedWindows.length - 1) {
        logCounter({
          name: "telemetry.flush.finished",
          value: 1,
          attributes: {},
        });
      } else {
        logCounter({
          name: "telemetry.flush.incomplete",
          value: completedWindows.length - stats.lastIndex,
          attributes: {},
        });
      }
    }

    if (stats.interrupted) {
      logCounter({
        name: "telemetry.flush.interrupted",
        value: 1,
        attributes: {},
      });
    }

    if (stats.lastDirtyCount) {
      logCounter({
        name: "telemetry.flush.last_dirty_count",
        value: stats.lastDirtyCount,
        attributes: {},
      });
    }

    logger.debug(
      `Flush completed. Processed ${completedWindows.length} time windows, skipped current window: ${currentWindow}`,
    );

    return flushed;
  },
);

const flushWindow = async (
  window: string,
  index: number,
  stats: Stats,
  onlyOrgId: string,
  flushed: Flush,
  canContinue: () => boolean,
) => {
  try {
    let windowInitialCount = 0;
    let isFirstIteration = true;
    stats.lastIndex = index;

    while (true) {
      if (!canContinue()) {
        stats.interrupted = true;
        break;
      }

      const { dirtyKeys, dirtyCount } = onlyOrgId
        ? await popDirtyEventKeysForOrgId(onlyOrgId, window)
        : await popDirtyEventKeys(window);

      stats.lastDirtyCount = dirtyCount;

      if (isFirstIteration) {
        isFirstIteration = false;
        windowInitialCount = dirtyKeys.length + dirtyCount;
        if (windowInitialCount > 0) {
          stats.totalInitialKeys += windowInitialCount;
          stats.windowsWithData++;
        }
      }

      // this may be expected; we naively lookup dirty keys
      if (!dirtyKeys.length) {
        break;
      }

      await batch(
        "flushWindowDirtyKeys",
        dirtyKeys.map((dirtyKey) => flushWindowDirtyKey(dirtyKey, flushed)),
      );

      logger.debug(
        `Processed ${dirtyKeys?.length || 0} keys, ${dirtyCount} remaining in window`,
      );
    }
  } catch (e) {
    logCounter({
      name: "telemetry.errors.flushWindow",
      value: 1,
      attributes: {},
    });

    logger.error("Failed to flush window", {
      error: makeError(e),
    });
  }
};

const flushWindowDirtyKey = async (dirtyKey: string, flushed: Flush) => {
  const { window: windowSlotStr, eventName, orgId } = parseDirtyKey(dirtyKey);

  try {
    const { token: encryptedToken, metrics } =
      await popSerializedEventData(dirtyKey);

    // Use the window slot to reconstruct the window start time
    const windowSlot = parseInt(windowSlotStr, 10);
    const windowDate = windowSlotToDate(windowSlot);

    const event: AnyEvent = {
      event_name: eventName,
      idempotency_key: `${orgId}-${eventName}-${windowSlot}`,
      external_customer_id: orgId,
      properties: { ...metrics, org_id: orgId, type: "aggregated" },
      timestamp: windowDate.toISOString(),
    };

    let store = flushed[orgId];
    if (!store) {
      store = { token: "", events: [] };
      flushed[orgId] = store;
    }

    // not ideal, but let's assume that any decryptable token for an org is the token of choice
    // could provide a unique list of tokens to attempt, if we needed to
    const token =
      (!store.token && encryptedToken && (await decrypt(encryptedToken))) || "";
    if (token) {
      store.token = token;
    }

    store.events.push(event);
  } catch (e) {
    logCounter({
      name: "telemetry.errors.flushWindowDirtyKey",
      value: 1,
      attributes: {
        eventName,
        orgId,
      },
    });

    logger.error("Failed to flush dirty event", {
      error: makeError(e),
    });
  }
};

const popSerializedEventData = async (dirtyKey: string) => {
  const { pipeline } = useRedisNamespace(TELEMETRY_AGGREGATION_NAMESPACE);

  const [data, _count] = await pipeline((client) => {
    client.hGetAll(dirtyKey);
    client.del(dirtyKey);
  });

  if (typeof data !== "object") {
    throw new Error(`Invalid data type: ${typeof data}`);
  }

  if (data === null) {
    throw new Error(`Invalid data: ${data}`);
  }

  let token = "";
  const metrics: Record<string, number> = {};

  for (const [key, value] of Object.entries(data)) {
    if (key === "token") {
      token = String(value);
    } else if (key.startsWith("metric:")) {
      const metricName = key.substring(7); // Remove "metric:" prefix
      metrics[metricName] = parseInt(String(value), 10);
    } else {
      throw new Error(`unknown key: ${key}`);
    }
  }

  return {
    token,
    metrics,
  };
};

const popDirtyEventKeys = async (
  dirtySegment: string,
  dirtyEventsCount = 1000,
) => {
  const { pipeline } = useRedisNamespace(TELEMETRY_AGGREGATION_NAMESPACE);

  const raw = await pipeline((client) => {
    client.sPop(dirtySegment, dirtyEventsCount);
    client.sCard(dirtySegment);
  });

  const [dirtyKeys, dirtyCount] = z
    .tuple([z.array(z.string()), z.coerce.number()])
    .parse(raw);

  return { dirtyKeys, dirtyCount };
};

// mostly used for testing but ensures we only flush events for particular orgs only
// buyer beware if used in production!
const popDirtyEventKeysForOrgId = async (
  orgId: string,
  dirtySegment: string,
) => {
  const { getClient, getKey } = useRedisNamespace(
    TELEMETRY_AGGREGATION_NAMESPACE,
  );

  const { currentWindow: thisWindow } = parseDirtySegment(dirtySegment);

  const client = await getClient();
  let cursor = 0;

  const dirtyKeys = [];
  const pattern = getKey(`${thisWindow}:${orgId}:*`);

  do {
    const reply = await client.sScan(dirtySegment, cursor, {
      MATCH: pattern,
      COUNT: 1000,
    });
    cursor = reply.cursor;
    dirtyKeys.push(...reply.members);
  } while (cursor !== 0);

  if (dirtyKeys?.length) {
    await client.sRem(dirtySegment, dirtyKeys);
  }

  return { dirtyKeys, dirtyCount: 0 };
};

export const getAggregationStatus = otelWrapTraced(
  "getAggregationStatus",
  async ({ orgIds }: { orgIds?: string[] }) => {
    // pending events grouped by orgId
    const orgs: Record<string, { events: number }> = {};

    let pendingWindows = 0;
    let pendingEvents = 0;

    const currentWindow = getCurrentWindow();
    const { getKey, getClient } = useRedisNamespace(
      TELEMETRY_AGGREGATION_NAMESPACE,
    );

    try {
      const client = await getClient();
      const maxWindowsToCheck = Math.ceil(
        WINDOW_TTL_MINUTES / WINDOW_SIZE_MINUTES,
      );

      // Check all windows including current window
      const windowsToCheck = Array.from(
        { length: maxWindowsToCheck + 1 },
        (_, i) => {
          const windowSlot = currentWindow - (maxWindowsToCheck - i);
          return getKey(`dirty:${windowSlot}`);
        },
      );

      for (const dirtyKey of windowsToCheck) {
        // Get all dirty event keys from this window
        const dirtyKeys = await client.sMembers(dirtyKey);
        if (!dirtyKeys.length) {
          continue;
        }

        pendingWindows++;

        for (const dirtyKey of dirtyKeys) {
          const { orgId } = parseDirtyKey(dirtyKey);
          pendingEvents++;

          if ((orgIds || []).includes(orgId)) {
            if (!orgs[orgId]) {
              orgs[orgId] = { events: 0 };
            }
            orgs[orgId].events++;
          }
        }
      }
    } catch (error) {
      logger.error("Failed to get aggregation status", {
        error: makeError(error),
      });
    }

    logger.debug(
      `Aggregation status: ${pendingWindows} pending windows, ${pendingEvents} pending events across ${Object.keys(orgs).length} orgs`,
    );

    return {
      state: {
        now: Date.now(),
        currentWindow,
        sleepIntervalSeconds: SLEEP_INTERVAL_SECONDS,
        windowSizeMinutes: WINDOW_SIZE_MINUTES,
        dataExpiresMinutes: WINDOW_TTL_MINUTES,
        cacheVersion: CACHE_VERSION,
      },
      pending: {
        windows: pendingWindows,
        events: pendingEvents,
      },
      orgs,
    };
  },
);
