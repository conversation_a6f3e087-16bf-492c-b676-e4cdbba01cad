import {
  Di<PERSON>atch<PERSON>,
  get<PERSON><PERSON><PERSON><PERSON><PERSON>patcher,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  setGlobalDispatcher,
} from "undici";
import {
  afterAll,
  afterEach,
  beforeAll,
  beforeEach,
  describe,
  expect,
  test,
  vi,
} from "vitest";
import { initializeGlobalDispatcher } from "../custom_fetch";
import { PENDING_FLUSHABLES } from "../pending_flushables";
import { createMockRedisClient } from "../redis.test";
import { AnyEvent } from "./types";
import { withTelemetry } from "./withTelemetry";

const mockRedis = createMockRedisClient();

vi.mock("../redis", () => ({
  getRedis: () => Promise.resolve(mockRedis.client),
}));

vi.mock("../env", async (importOriginal) => {
  const actual = await importOriginal<typeof import("../env")>();
  return {
    ...actual,
    ALLOWED_ORIGIN: "http://localhost:3000",
    TELEMETRY_URL: "http://localhost:3000/events",
    TELEMETRY_TOKEN: undefined,
    TELEMETRY_ENABLED: true,
  };
});

let mockAgent: MockAgent;
let originalDispatcher: Dispatcher;
let mockPool: MockPool;

beforeAll(async () => {
  originalDispatcher = getGlobalDispatcher();
});

beforeEach(async () => {
  vi.useFakeTimers();
  mockAgent = new MockAgent();
  mockAgent.disableNetConnect();

  const env = await import("../env");
  vi.mocked(env).TELEMETRY_ENABLED = true;

  mockPool = mockAgent.get("http://localhost:3000");
  initializeGlobalDispatcher({ agent: mockAgent, force: true });

  mockRedis.store.clear();
  await PENDING_FLUSHABLES.flush();

  // Set telemetry URL for all tests
  vi.mocked(await import("../env")).TELEMETRY_URL =
    "http://localhost:3000/events";
});

afterEach(async () => {
  await mockAgent.close();
  vi.clearAllMocks();
  vi.useRealTimers();
});

afterAll(() => {
  setGlobalDispatcher(originalDispatcher);
});

describe("withTelemetry", () => {
  test("skips telemetry when TELEMETRY_ENABLED is false", async () => {
    // Temporarily set TELEMETRY_ENABLED to false for this test
    const env = await import("../env");
    vi.mocked(env).TELEMETRY_ENABLED = false;

    const testEvent: AnyEvent = {
      event_name: "test_event",
      idempotency_key: "test-key",
      timestamp: new Date().toISOString(),
      external_customer_id: "test-customer",
      properties: {
        test: "value",
      },
    };

    const mockFn = vi.fn().mockResolvedValue("test-result");
    const mockGenerator = vi.fn().mockResolvedValue({
      events: [testEvent],
      token: "test-token",
      appOrigin: "http://localhost:3000",
    });

    const wrappedFn = withTelemetry(mockFn, mockGenerator);
    const result = await wrappedFn("arg1", "arg2");

    await PENDING_FLUSHABLES.flush();

    // Function should work normally
    expect(result).toBe("test-result");
    expect(mockFn).toHaveBeenCalledWith("arg1", "arg2");

    // But generator should never be called and no telemetry should be sent
    expect(mockGenerator).not.toHaveBeenCalled();
    expect(PENDING_FLUSHABLES.size).toBe(0);
    mockAgent.assertNoPendingInterceptors();
  });

  test("basic function wrapping", async () => {
    const testEvent: AnyEvent = {
      event_name: "test_event",
      idempotency_key: "test-key",
      timestamp: new Date().toISOString(),
      external_customer_id: "test-customer",
      properties: {
        test: "value",
        user_id: "123e4567-e89b-12d3-a456-426614174000",
        app_origin: "http://localhost:3000",
        type: "individual",
      },
    };

    // Mock /api/self/me endpoint
    mockPool
      .intercept({
        path: "/api/self/me",
        method: "POST",
      })
      .reply(200, {
        id: "123e4567-e89b-12d3-a456-426614174000",
        email: "<EMAIL>",
        organizations: [],
      })
      .persist();

    // Mock the telemetry endpoint
    mockPool
      .intercept({
        path: "/events",
        method: "POST",
        body: JSON.stringify({ events: [testEvent] }),
        headers: {
          Authorization: "Bearer test-token",
          "Content-Type": "application/json",
        },
      })
      .reply(200, { success: true });

    const mockFn = vi.fn().mockResolvedValue("test-result");
    const mockGenerator = vi.fn().mockResolvedValue({
      events: [testEvent],
      token: "test-token",
      appOrigin: "http://localhost:3000",
    });

    const wrappedFn = withTelemetry(mockFn, mockGenerator);
    const result = await wrappedFn("arg1", "arg2");

    await PENDING_FLUSHABLES.flush();

    expect(result).toBe("test-result");
    expect(mockFn).toHaveBeenCalledWith("arg1", "arg2");
    expect(mockGenerator).toHaveBeenCalledWith(
      expect.objectContaining({
        args: ["arg1", "arg2"],
        output: "test-result",
        stats: expect.objectContaining({
          duration_ms: expect.any(Number),
        }),
      }),
    );
    mockAgent.assertNoPendingInterceptors();
  });

  test("handles failed telemetry requests gracefully", async () => {
    const testEvent: AnyEvent = {
      event_name: "test_event",
      idempotency_key: "test-key",
      timestamp: new Date().toISOString(),
      external_customer_id: "test-customer",
      properties: {
        test: "value",
        user_id: "123e4567-e89b-12d3-a456-426614174000",
        app_origin: "http://localhost:3000",
        type: "individual",
      },
    };

    // Mock /api/self/me endpoint
    mockPool
      .intercept({
        path: "/api/self/me",
        method: "POST",
      })
      .reply(200, {
        id: "123e4567-e89b-12d3-a456-426614174000",
        email: "<EMAIL>",
        organizations: [],
      })
      .persist();

    let requestCount = 0;
    mockPool
      .intercept({
        path: "/events",
        method: "POST",
        body: JSON.stringify({ events: [testEvent] }),
      })
      .reply(() => {
        requestCount++;
        return {
          statusCode: 500,
          data: JSON.stringify({ error: "Internal Server Error" }),
        };
      })
      .persist();

    const mockFn = vi.fn().mockResolvedValue("test-result");
    const mockGenerator = vi.fn().mockResolvedValue({
      events: [testEvent],
      token: "test-token",
      appOrigin: "http://localhost:3000",
    });

    const wrappedFn = withTelemetry(mockFn, mockGenerator);
    const result = await wrappedFn();

    await vi.runAllTimersAsync();

    // Function should complete successfully even if telemetry fails
    expect(result).toBe("test-result");

    // Telemetry request should have been attempted
    expect(requestCount).toBe(1);
    mockAgent.assertNoPendingInterceptors();
  });

  test("handles invalid events", async () => {
    let requestCount = 0;
    // Mock the telemetry endpoint - this should never be called since all events are invalid
    mockPool
      .intercept({
        path: "/events",
        method: "POST",
      })
      .reply(() => {
        requestCount++;
        return {
          statusCode: 200,
          data: JSON.stringify({ success: true }),
        };
      });

    const mockFn = vi.fn().mockResolvedValue("test-result");
    const mockGenerator = vi.fn().mockResolvedValue({
      events: [
        // Missing required fields: event_name, idempotency_key, timestamp, external_customer_id
        { invalid: "event" } as any,
        // Invalid timestamp format
        {
          event_name: "test",
          idempotency_key: "key",
          timestamp: "not-a-date",
          external_customer_id: "org-id",
          properties: {},
        } as any,
        // Invalid properties type
        {
          event_name: "test",
          idempotency_key: "key",
          timestamp: new Date().toISOString(),
          external_customer_id: "org-id",
          properties: {
            invalid: { nested: "object" },
          },
        } as any,
      ],
      token: undefined,
      appOrigin: "http://localhost:3000",
    });

    const wrappedFn = withTelemetry(mockFn, mockGenerator);
    const result = await wrappedFn();

    await vi.runAllTimersAsync();
    await PENDING_FLUSHABLES.flush();

    expect(result).toBe("test-result");

    // No events should be queued for sending since they're all invalid
    expect(PENDING_FLUSHABLES.size).toBe(0);

    // The telemetry endpoint should never be called since all events are invalid
    expect(requestCount).toBe(0);
    expect(mockAgent.pendingInterceptors().length).toBe(1);
  });

  test("handles generator errors", async () => {
    const mockFn = vi.fn().mockResolvedValue("test-result");
    const mockGenerator = vi
      .fn()
      .mockRejectedValue(new Error("Generator error"));

    const wrappedFn = withTelemetry(mockFn, mockGenerator);
    const result = await wrappedFn();

    await vi.runAllTimersAsync();
    await PENDING_FLUSHABLES.flush();

    expect(result).toBe("test-result");
    expect(PENDING_FLUSHABLES.size).toBe(0);
    mockAgent.assertNoPendingInterceptors();
  });

  test("handles function errors", async () => {
    const testError = new Error("Function error");
    const mockFn = vi.fn().mockRejectedValue(testError);
    const mockGenerator = vi.fn();

    const wrappedFn = withTelemetry(mockFn, mockGenerator);

    await expect(wrappedFn()).rejects.toThrow(testError);
    expect(mockGenerator).not.toHaveBeenCalled();
    mockAgent.assertNoPendingInterceptors();
  });

  test("handles auth token validation failure", async () => {
    const testEvent: AnyEvent = {
      event_name: "test_event",
      idempotency_key: "test-key",
      timestamp: new Date().toISOString(),
      external_customer_id: "test-customer",
      properties: {
        test: "value",
      },
    };

    // Mock /api/self/me endpoint to fail
    mockPool
      .intercept({
        path: "/api/self/me",
        method: "POST",
      })
      .reply(400, {
        error: {
          message: "User does not exist",
          status: 400,
        },
      })
      .persist();

    let requestCount = 0;
    mockPool
      .intercept({
        path: "/events",
        method: "POST",
      })
      .reply(() => {
        requestCount++;
        return {
          statusCode: 200,
          data: JSON.stringify({ success: true }),
        };
      });

    const mockFn = vi.fn().mockResolvedValue("test-result");
    const mockGenerator = vi.fn().mockResolvedValue({
      events: [testEvent],
      token: "invalid-token",
      appOrigin: "http://localhost:3000",
    });

    const wrappedFn = withTelemetry(mockFn, mockGenerator);
    const result = await wrappedFn();

    await vi.runAllTimersAsync();
    await PENDING_FLUSHABLES.flush();

    expect(result).toBe("test-result");

    // Events should still be sent but with anon user
    // (let the events endpoint deal with the token issue)

    expect(PENDING_FLUSHABLES.size).toBe(0);
    expect(requestCount).toBe(1);
    mockAgent.assertNoPendingInterceptors();
  });

  test("handles undefined events from generator", async () => {
    const mockFn = vi.fn().mockResolvedValue("test-result");
    const mockGenerator = vi.fn().mockResolvedValue({
      events: [undefined, undefined] as any,
      token: "test-token",
      appOrigin: "http://localhost:3000",
    });

    let requestCount = 0;
    mockPool
      .intercept({
        path: "/events",
        method: "POST",
      })
      .reply(() => {
        requestCount++;
        return {
          statusCode: 200,
          data: JSON.stringify({ success: true }),
        };
      });

    const wrappedFn = withTelemetry(mockFn, mockGenerator);
    const result = await wrappedFn();

    await vi.runAllTimersAsync();
    await PENDING_FLUSHABLES.flush();

    expect(result).toBe("test-result");
    // No events should be queued since they were all undefined
    expect(PENDING_FLUSHABLES.size).toBe(0);
    expect(requestCount).toBe(0);
    expect(mockAgent.pendingInterceptors().length).toBe(1);
  });

  test("handles rate limiting for telemetry configs", async () => {
    const startTime = Date.now();
    vi.setSystemTime(startTime);

    // Disable TELEMETRY_URL to force config lookups
    vi.mocked(await import("../env")).TELEMETRY_URL = undefined;

    const orgId = "123e4567-e89b-12d3-a456-426614174000";

    const testEvent: AnyEvent = {
      event_name: "test_event",
      idempotency_key: "test-key",
      timestamp: new Date().toISOString(),
      external_customer_id: orgId,
      properties: {
        test: "value",
      },
    };

    // Mock /api/self/me endpoint
    mockPool
      .intercept({
        path: "/api/self/me",
        method: "POST",
      })
      .reply(200, {
        id: "123e4567-e89b-12d3-a456-426614174000",
        email: "<EMAIL>",
        organizations: [{ id: orgId, name: "test-org" }],
      })
      .persist();

    // Mock /api/telemetry/configs endpoint with rate limiting
    let configRequestCount = 0;
    mockPool
      .intercept({
        path: "/api/telemetry/configs",
        method: "POST",
        body: JSON.stringify({ org_ids: [orgId] }),
      })
      .reply(() => {
        configRequestCount++;
        if (configRequestCount <= 5) {
          return {
            statusCode: 200,
            data: JSON.stringify({
              configs: {
                [orgId]: {
                  url: "http://localhost:3000/events",
                  secret: "test-secret",
                },
              },
            }),
          };
        }
        return {
          statusCode: 429,
          data: JSON.stringify({ error: "Too Many Requests" }),
        };
      })
      .persist();

    // Mock events endpoint - this should still work even when configs is rate limited
    let eventsRequestCount = 0;
    mockPool
      .intercept({
        path: "/events",
        method: "POST",
      })
      .reply(() => {
        eventsRequestCount++;
        return {
          statusCode: 200,
          data: JSON.stringify({ success: true }),
        };
      })
      .persist();

    const mockFn = vi.fn().mockResolvedValue("test-result");
    const mockGenerator = vi.fn().mockResolvedValue({
      events: [testEvent],
      token: "test-token",
      appOrigin: "http://localhost:3000",
    });

    const wrappedFn = withTelemetry(mockFn, mockGenerator);

    // Make requests up to the rate limit
    for (let i = 0; i < 5; i++) {
      const result = await wrappedFn();
      expect(result).toBe("test-result");
      await PENDING_FLUSHABLES.flush();
    }

    // Next request should still work for the wrapped function but config lookup should be rate limited
    const result = await wrappedFn();
    expect(result).toBe("test-result");
    await PENDING_FLUSHABLES.flush();

    expect(configRequestCount).toBe(1); // since we cache!

    // Events should still be sent. One for each call to wrappedFn
    expect(eventsRequestCount).toBe(6);

    // Move time forward past the rate limit window
    vi.setSystemTime(startTime + 60 * 1000); // 1 minute later

    // Should be able to get configs again
    const finalResult = await wrappedFn();
    expect(finalResult).toBe("test-result");
    await PENDING_FLUSHABLES.flush();

    expect(configRequestCount).toBe(1); // still 1 config lookup!
    expect(eventsRequestCount).toBe(7);
    mockAgent.assertNoPendingInterceptors();
  });
});
