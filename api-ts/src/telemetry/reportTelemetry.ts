import { makeError } from "@braintrust/local";
import { aggregate, updateStore } from "./aggregation";
import { getTelemetryConfigs } from "./getTelemetryConfig";
import { logger } from "./logger";
import { request } from "./request";
import { AnyEvent, TelemetryConfig } from "./types";

export async function reportTelemetry({
  events: unprocessed,
  token,
}: {
  events: AnyEvent[];
  token: string | undefined;
}): Promise<void> {
  const { original: _, aggregated } = await aggregate(unprocessed);

  await Promise.allSettled([
    updateStore({ events: Object.values(aggregated), token }),
    // XXX: for now we'll always push all of the events to avoid any potential data loss
    // eventually we should push original + aggregated to telemetry directly in case redis is down
    pushTelemetryEvents({
      events: unprocessed.map((event) => {
        return {
          ...event,
          properties: { ...event.properties, type: "individual" },
        };
      }),
      token,
    }).catch((e) => {
      logger.error("Error reporting telemetry", { error: makeError(e) });
    }),
  ]);
}

export const pushTelemetryEvents = async ({
  events,
  token,
}: {
  events: AnyEvent[];
  token: string | undefined;
}): Promise<void> => {
  if (!events.length) {
    logger.debug("Skipping report due to no events");
    return;
  }

  const orgIds = events.map((event) => event.external_customer_id);
  const configs = await getTelemetryConfigs({ token, orgIds });

  // In most cases there will only be one orgId, but this way
  // we can report to multiple orgs and their respective configs as needed
  const groupedEvents = new Map<
    string,
    Parameters<typeof reportTelemetryWithConfig>[0]
  >();

  for (const event of events) {
    const orgId = event.external_customer_id;
    const config = configs[orgId];
    const key = `${config?.url}-${config?.secret}`;
    if (!groupedEvents.has(key)) {
      groupedEvents.set(key, { config, orgId, events: [] });
    }
    groupedEvents.get(key)?.events.push(event);
  }

  await Promise.all([...groupedEvents.values()].map(reportTelemetryWithConfig));
};

// Orb doesn't support undefined/null values, so we need to replace them with a well known string.
const MISSING = "__UNSET__";

async function reportTelemetryWithConfig({
  events,
  orgId,
  config: { url, secret },
}: {
  events: AnyEvent[];
  orgId: string;
  config: TelemetryConfig;
}) {
  if (!events.length) {
    logger.debug(`Skipping telemetry report to ${orgId} due to no events`);
    return;
  }

  if (!url || !secret) {
    logger.debug(`Skipping telemetry report to ${orgId} due to missing config`);
    return;
  }

  const response = await request(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${secret}`,
    },
    body: JSON.stringify({
      events: events.map((event) => ({
        ...event,
        properties: Object.fromEntries(
          Object.entries(event.properties).map(([key, value]) => [
            key,
            value ?? MISSING,
          ]),
        ),
      })),
    }),
  });

  if (!response.ok) {
    const body = await response.body.json();
    throw new Error(`${response.statusCode} ${JSON.stringify(body)}`);
  }
}
