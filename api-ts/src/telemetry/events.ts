import { z } from "zod";
import { BaseEventSchema } from "./types";

export const FunctionInvokedEventSchema = BaseEventSchema(
  z.literal("FunctionInvokedEvent"),
  z.object({
    org_id: z.string(),
    count: z.number().nullish(),

    // We used to emit this event on every insert, but due to the high volume we've started to
    // pre-aggregate. Now that we're pre-aggregating, some properties are no longer possible.
    xact_id: z.string().nullish().describe("deprecated"),
    app_origin: z.string().nullish().describe("deprecated"),
    proxy_url: z.string().nullish().describe("deprecated"),
    project_id: z.string().nullish().describe("deprecated"),
    row_id: z.string().nullish().describe("deprecated"),
    span_id: z.string().nullish().describe("deprecated"),
    root_span_id: z.string().nullish().describe("deprecated"),
    span_parents_csv: z.string().nullish().describe("deprecated"),
    mode: z.string().nullish().describe("deprecated"),
    timeout_ms: z.number().nullish().describe("deprecated"),
    messages_bytes: z.number().nullish().describe("deprecated"),
    input_bytes: z.number().nullish().describe("deprecated"),
    function_id: z.string().nullish().describe("deprecated"),
    function_type: z.string().nullish().describe("deprecated"),
    function_data_type: z.string().nullish().describe("deprecated"),
    function_data_bytes: z.number().nullish().describe("deprecated"),
    prompt_data_prompt_type: z.string().nullish().describe("deprecated"),
    prompt_data_bytes: z.number().nullish().describe("deprecated"),
    invoke_method_type: z.string().nullish().describe("deprecated"),
    duration_ms: z.number().nullish().describe("deprecated"),

    // TEMP: while we audit pre-aggregation
    agg_count: z.number().nullish().describe("temporary"),

    // NOTE: scope reduction: in the future we could group by project_id, and
    //   aggregate various props by _min, _max, _mean, _sum_squares if we needed to use billing
    //   as an analytical store
  }),
).describe("DEPRECATED see AggregatedFunctionInvokedEventSchema");

export type FunctionInvokedEvent = z.infer<typeof FunctionInvokedEventSchema>;

export const LogInsertedEventSchema = BaseEventSchema(
  z.literal("LogInsertedEvent"),
  z.object({
    org_id: z.string(),
    count: z.number().nullish(),

    // Note: while we're auditing pre-aggregation, log_bytes, metrics_count, scores_count will be 0 to avoid double counting
    log_bytes: z.number(),
    metrics_count: z.number(),
    scores_count: z.number(),

    // We used to emit this event on every insert, but due to the high volume we've started to
    // pre-aggregate. Now that we're pre-aggregating, some properties are no longer possible.
    object_type: z.string().nullish().describe("deprecated"),
    object_id: z.string().nullish().describe("deprecated"),
    row_id: z.string().nullish().describe("deprecated"),
    project_id: z.string().nullish().describe("deprecated"),
    xact_id: z.string().nullish().describe("deprecated"),
    span_attributes_type: z.string().nullish().describe("deprecated"),
    span_id: z.string().nullish().describe("deprecated"),
    root_span_id: z.string().nullish().describe("deprecated"),
    span_parents_csv: z.string().nullish().describe("deprecated"),

    // TEMP: while we audit pre-aggregation
    agg_metrics_count: z.number().nullish().describe("temporary"),
    agg_scores_count: z.number().nullish().describe("temporary"),
    agg_log_bytes: z.number().nullish().describe("temporary"),
    type: z.string().nullish().describe("temp"), // helpful to mark which event this is
  }),
);

export type LogInsertedEvent = z.infer<typeof LogInsertedEventSchema>;
