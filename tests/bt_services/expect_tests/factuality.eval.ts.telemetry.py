from unittest.mock import ANY

from tests.helpers.datetime import iso8601
from tests.helpers.number import number
from tests.helpers.telemetry import csv, ctx, idempotency, prop, unset
from tests.helpers.uuid import uuid
from tests.helpers.xact_id import xact_id


def task():
    return {
        "individual": [
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "properties": {
                    "metrics_count": 0,
                    "object_type": "experiment",
                    "span_attributes_type": "eval",
                },
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "properties": {
                    "metrics_count": 0,
                    "object_type": "experiment",
                    "span_attributes_type": "task",
                },
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "properties": {
                    "metrics_count": 0,
                    "object_type": "experiment",
                },
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "properties": {
                    "metrics_count": 0,
                    "scores_count": 1,
                    "span_attributes_type": "score",
                },
            },
            {
                "event_name": "LogInsertedEvent",
                "external_customer_id": ctx("org_id"),
                "properties": {
                    "metrics_count": 0,
                    "object_type": "experiment",
                    "scores_count": 0,
                    "span_attributes_type": "llm",
                },
            },
        ],
        "aggregated": [
            {
                "event_name": "LogInsertedEvent",
                "idempotency_key": ANY,
                "external_customer_id": ctx("org_id"),
                "properties": {
                    "log_bytes": 0,
                    "metrics_count": 0,
                    "scores_count": 0,
                    "agg_log_bytes": number(gte=1),
                    "agg_metrics_count": 0,
                    "agg_scores_count": number(gte=1),
                    "count": number(gte=1),
                },
                "timestamp": iso8601(),
            },
        ],
    }
